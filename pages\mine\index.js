const app = getApp();

Page({
  data: {
    userInfo: {},
    formattedPhone: ''
  },

  onLoad() {
    const user_info = wx.getStorageSync('userInfo')
    this.setData({
      userInfo: user_info
    }, () => {
      this.formatPhoneNumber();
    })
  },

  formatPhoneNumber() {
    const {
      phone
    } = this.data.userInfo;
    // 格式化手机号为 138****8888 格式
    if (!phone) return;
    const formattedPhone = phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    this.setData({
      formattedPhone
    });
  },

  goToFamilyMessage() {
    wx.navigateTo({
      url: '/pages/family_message/index'
    });
  },

  goToNotice() {
    wx.navigateTo({
      url: '/pages/message/index'
    });
  },

  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: res => {
        if (res.confirm) {
          // 清除用户信息
          app.globalData.userInfo = null;
          // 清空缓存 
          const tem = wx.getStorageSync('savedAccount')
          wx.clearStorageSync()
          if (tem) {
            wx.setStorageSync('savedAccount', tem)
          }
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  }
});