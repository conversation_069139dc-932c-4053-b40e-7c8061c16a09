import { http } from '@/utils/request'

// 通知相关API
export const notificationApi = {
  // 获取通知列表
  getNotifications: (params) => http.get('/notifications', params),
  
  // 获取通知详情
  getNotificationDetail: (id) => http.get(`/notifications/${id}`),
  
  // 创建通知
  createNotification: (data) => http.post('/notifications', data),
  
  // 更新通知
  updateNotification: (id, data) => http.put(`/notifications/${id}`, data),
  
  // 删除通知
  deleteNotification: (id) => http.delete(`/notifications/${id}`),
  
  // 发布通知
  publishNotification: (id) => http.put(`/notifications/${id}/publish`),
  
  // 撤回通知
  unpublishNotification: (id) => http.put(`/notifications/${id}/unpublish`),
  
  // 批量删除通知
  batchDelete: (ids) => http.delete('/notifications/batch', { data: { ids } }),
  
  // 获取通知统计
  getNotificationStatistics: () => http.get('/notifications/statistics')
}
