/* SCSS Mixins 和工具类 - 小程序兼容版本 */

// 颜色变量 (使用 SCSS 变量而不是 CSS 变量)
$primary-200: #bfdbfe;
$primary-500: #3b82f6;
$primary-600: #2563eb;
$primary-700: #1d4ed8;
$pink-500: #ec4899;
$pink-600: #db2777;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;
$green-500: #10b981;
$green-600: #059669;
$red-500: #ef4444;
$red-600: #dc2626;

// 阴影变量
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// 间距变量
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-2xl: 48rpx;

// 圆角变量
$radius-sm: 8rpx;
$radius: 12rpx;
$radius-md: 16rpx;
$radius-lg: 24rpx;

// Mixins

// 卡片样式 mixin
@mixin card($padding: $spacing-xl, $radius: $radius-md, $shadow: $shadow-md) {
  background: white;
  border-radius: $radius;
  box-shadow: $shadow;
  padding: $padding;
  border: 2rpx solid $gray-200;
}

// 按钮样式 mixin
@mixin button($bg: $primary-600, $color: white, $padding: 20rpx 32rpx, $radius: $radius) {
  background-color: $bg;
  color: $color;
  border: none;
  border-radius: $radius;
  padding: $padding;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
  box-sizing: border-box;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

// 渐变按钮 mixin
@mixin gradient-button($from: $primary-500, $to: $pink-500, $color: white) {
  background: linear-gradient(135deg, $from, $to);
  color: $color;
  border: none;
  border-radius: $radius;
  padding: 20rpx 32rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
  box-sizing: border-box;
  box-shadow: $shadow-md;

  &:active {
    transform: scale(0.98);
  }
}

// 输入框样式 mixin
@mixin input($border-color: $gray-200, $focus-color: $primary-500) {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid $border-color;
  border-radius: $radius;
  font-size: 28rpx;
  background-color: white;
  color: $gray-900;
  transition: border-color 0.2s ease;
  box-sizing: border-box;

  &:focus {
    border-color: $focus-color;
    outline: none;
    box-shadow: 0 0 0 6rpx rgba($focus-color, 0.1);
  }

  &::placeholder {
    color: $gray-400;
  }
}

// Flexbox 布局 mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 文本样式 mixins
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 响应式 mixins (小程序中使用媒体查询替代方案)
@mixin mobile-only {

  // 小程序中可以使用条件渲染或者 rpx 单位来实现响应式
  @media (max-width: 750rpx) {
    @content;
  }
}

// 动画 mixins
@mixin fade-in($duration: 0.3s) {
  animation: fadeIn $duration ease-out;
}

@mixin slide-up($duration: 0.3s) {
  animation: slideUp $duration ease-out;
}

// 工具类生成器
@mixin generate-spacing-classes {
  .p-xs {
    padding: $spacing-xs;
  }

  .p-sm {
    padding: $spacing-sm;
  }

  .p-md {
    padding: $spacing-md;
  }

  .p-lg {
    padding: $spacing-lg;
  }

  .p-xl {
    padding: $spacing-xl;
  }

  .m-xs {
    margin: $spacing-xs;
  }

  .m-sm {
    margin: $spacing-sm;
  }

  .m-md {
    margin: $spacing-md;
  }

  .m-lg {
    margin: $spacing-lg;
  }

  .m-xl {
    margin: $spacing-xl;
  }

  .mb-xs {
    margin-bottom: $spacing-xs;
  }

  .mb-sm {
    margin-bottom: $spacing-sm;
  }

  .mb-md {
    margin-bottom: $spacing-md;
  }

  .mb-lg {
    margin-bottom: $spacing-lg;
  }

  .mb-xl {
    margin-bottom: $spacing-xl;
  }
}

// 生成工具类
@include generate-spacing-classes;

// 动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

// 常用组合样式
.card-primary {
  @include card;
  border-color: $primary-200;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, $primary-500, $pink-500);
    border-radius: $radius-md $radius-md 0 0;
  }
}

.btn-primary {
  @include button($primary-600);
}

.btn-gradient {
  @include gradient-button;
}

.input-primary {
  @include input;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.text-truncate {
  @include text-truncate;
}