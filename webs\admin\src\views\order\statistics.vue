<template>
  <div class="order-statistics">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in orderStats" :key="index">
        <StatsCard
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :type="stat.type"
          :trend="stat.trend"
          :description="stat.description"
          :animated="true"
        />
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 订单趋势图 -->
      <el-col :xs="24" :lg="16">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单趋势分析</h3>
            <div class="chart-controls">
              <el-radio-group v-model="trendPeriod" size="small" @change="updateTrendChart">
                <el-radio-button label="7d">近7天</el-radio-button>
                <el-radio-button label="30d">近30天</el-radio-button>
                <el-radio-button label="90d">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="trendChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 订单状态分布 -->
      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单状态分布</h3>
          </div>
          <div ref="statusChartRef" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 时间分布和热门时段 -->
    <el-row :gutter="20" class="analysis-section">
      <!-- 订单时间分布 -->
      <el-col :xs="24" :lg="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单时间分布</h3>
            <el-button size="small" @click="refreshTimeChart">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div ref="timeChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 热门菜品订单统计 -->
      <el-col :xs="24" :lg="12">
        <div class="data-card">
          <div class="card-header">
            <h3>热门菜品订单统计</h3>
            <el-button size="small" @click="refreshHotDishes">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="hot-dishes-chart">
            <div ref="hotDishesChartRef" class="chart-container"></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <CustomTable
        title="订单详细统计"
        :data="tableData"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        :show-search="true"
        :search-fields="searchFields"
        @search="handleSearch"
        @reset="handleReset"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
        <template #actions>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
        </template>
        
        <template #orderCount="{ row }">
          <el-tag type="primary">{{ row.orderCount }}单</el-tag>
        </template>
        
        <template #revenue="{ row }">
          <span class="revenue-text">¥{{ row.revenue }}</span>
        </template>
        
        <template #avgOrderValue="{ row }">
          <span class="avg-value">¥{{ row.avgOrderValue }}</span>
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { Refresh, Download, ShoppingCart, TrendCharts, Timer, Wallet } from '@element-plus/icons-vue'
import StatsCard from '@/components/StatsCard.vue'
import CustomTable from '@/components/CustomTable.vue'
import { orderApi } from '@/api/order'
import dayjs from 'dayjs'

const trendChartRef = ref()
const statusChartRef = ref()
const timeChartRef = ref()
const hotDishesChartRef = ref()
const trendPeriod = ref('7d')
const tableLoading = ref(false)
const tableData = ref([])

let trendChart = null
let statusChart = null
let timeChart = null
let hotDishesChart = null

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  date: '',
  status: ''
})

// 订单统计数据
const orderStats = reactive([
  {
    title: '今日订单',
    value: 0,
    icon: ShoppingCart,
    type: 'primary',
    trend: '+12%',
    description: '较昨日'
  },
  {
    title: '今日营收',
    value: 0,
    icon: Wallet,
    type: 'success',
    trend: '+8%',
    description: '较昨日'
  },
  {
    title: '平均订单价值',
    value: 0,
    icon: TrendCharts,
    type: 'warning',
    trend: '+5%',
    description: '较昨日'
  },
  {
    title: '平均处理时间',
    value: 0,
    icon: Timer,
    type: 'info',
    trend: '-3分钟',
    description: '较昨日'
  }
])

// 表格列配置
const columns = [
  { prop: 'date', label: '日期', width: 120, formatter: (row) => formatDate(row.date) },
  { prop: 'orderCount', label: '订单数量', width: 120, slot: true },
  { prop: 'revenue', label: '营收金额', width: 120, slot: true },
  { prop: 'avgOrderValue', label: '平均订单价值', width: 140, slot: true },
  { prop: 'completedOrders', label: '完成订单', width: 100 },
  { prop: 'cancelledOrders', label: '取消订单', width: 100 },
  { prop: 'completionRate', label: '完成率', width: 100, formatter: (row) => `${row.completionRate}%` }
]

// 搜索字段配置
const searchFields = [
  { prop: 'date', label: '日期', type: 'daterange' },
  { prop: 'status', label: '状态', type: 'select', options: [
    { label: '全部', value: '' },
    { label: '已完成', value: 'completed' },
    { label: '进行中', value: 'processing' },
    { label: '已取消', value: 'cancelled' }
  ]}
]

// 方法
const loadOrderStats = async () => {
  try {
    const res = await orderApi.getOrderStatistics()
    if (res.data) {
      orderStats[0].value = res.data.todayOrders || 45
      orderStats[1].value = `¥${res.data.todayRevenue || 1280}`
      orderStats[2].value = `¥${res.data.avgOrderValue || 28.5}`
      orderStats[3].value = `${res.data.avgProcessTime || 15}分钟`
    }
  } catch (error) {
    console.error('加载订单统计失败:', error)
    // 使用模拟数据
    orderStats[0].value = 45
    orderStats[1].value = '¥1,280'
    orderStats[2].value = '¥28.5'
    orderStats[3].value = '15分钟'
  }
}

const loadTableData = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }
    const res = await orderApi.getOrderStatistics(params)
    if (res.data) {
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    }
  } catch (error) {
    console.error('加载表格数据失败:', error)
    // 使用模拟数据
    tableData.value = generateMockTableData()
    pagination.total = 30
  } finally {
    tableLoading.value = false
  }
}

const generateMockTableData = () => {
  const data = []
  for (let i = 0; i < 10; i++) {
    const orderCount = Math.floor(Math.random() * 50) + 20
    const revenue = Math.floor(Math.random() * 1000) + 500
    const completed = Math.floor(orderCount * 0.8)
    const cancelled = orderCount - completed
    
    data.push({
      id: i + 1,
      date: dayjs().subtract(i, 'day').format('YYYY-MM-DD'),
      orderCount,
      revenue,
      avgOrderValue: Math.round(revenue / orderCount * 100) / 100,
      completedOrders: completed,
      cancelledOrders: cancelled,
      completionRate: Math.round(completed / orderCount * 100)
    })
  }
  return data
}

const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  updateTrendChart()
}

const updateTrendChart = () => {
  if (!trendChart) return
  
  const days = trendPeriod.value === '7d' ? 7 : trendPeriod.value === '30d' ? 30 : 90
  const dates = []
  const orders = []
  const revenue = []
  
  for (let i = days - 1; i >= 0; i--) {
    dates.push(dayjs().subtract(i, 'day').format('MM-DD'))
    orders.push(Math.floor(Math.random() * 50) + 20)
    revenue.push(Math.floor(Math.random() * 1000) + 500)
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['订单数', '营收']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '订单数',
        position: 'left'
      },
      {
        type: 'value',
        name: '营收(元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '订单数',
        type: 'line',
        data: orders,
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '营收',
        type: 'bar',
        yAxisIndex: 1,
        data: revenue,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

const initStatusChart = () => {
  if (!statusChartRef.value) return
  
  statusChart = echarts.init(statusChartRef.value)
  
  const data = [
    { value: 65, name: '已完成', itemStyle: { color: '#67C23A' } },
    { value: 25, name: '进行中', itemStyle: { color: '#E6A23C' } },
    { value: 8, name: '已取消', itemStyle: { color: '#F56C6C' } },
    { value: 2, name: '待确认', itemStyle: { color: '#909399' } }
  ]
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '订单状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChart.setOption(option)
}

const initTimeChart = () => {
  if (!timeChartRef.value) return
  
  timeChart = echarts.init(timeChartRef.value)
  refreshTimeChart()
}

const refreshTimeChart = () => {
  if (!timeChart) return
  
  const hours = []
  const orders = []
  
  for (let i = 6; i <= 22; i++) {
    hours.push(i + ':00')
    // 模拟用餐高峰期
    let orderCount
    if (i >= 11 && i <= 13) { // 午餐高峰
      orderCount = Math.floor(Math.random() * 30) + 40
    } else if (i >= 17 && i <= 19) { // 晚餐高峰
      orderCount = Math.floor(Math.random() * 35) + 45
    } else {
      orderCount = Math.floor(Math.random() * 15) + 5
    }
    orders.push(orderCount)
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '订单数',
        type: 'line',
        data: orders,
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        },
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }
  
  timeChart.setOption(option)
}

const initHotDishesChart = () => {
  if (!hotDishesChartRef.value) return
  
  hotDishesChart = echarts.init(hotDishesChartRef.value)
  refreshHotDishes()
}

const refreshHotDishes = () => {
  if (!hotDishesChart) return
  
  const dishes = ['红烧肉', '宫保鸡丁', '清炒时蔬', '紫菜蛋花汤', '米饭']
  const orders = [156, 142, 128, 98, 89]
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: dishes
    },
    series: [
      {
        name: '订单数',
        type: 'bar',
        data: orders,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#409EFF' },
              { offset: 1, color: '#67C23A' }
            ]
          }
        }
      }
    ]
  }
  
  hotDishesChart.setOption(option)
}

const formatDate = (date) => {
  return dayjs(date).format('MM-DD')
}

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadTableData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTableData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadTableData()
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 窗口大小变化时重新渲染图表
const handleResize = () => {
  if (trendChart) trendChart.resize()
  if (statusChart) statusChart.resize()
  if (timeChart) timeChart.resize()
  if (hotDishesChart) hotDishesChart.resize()
}

onMounted(async () => {
  await loadOrderStats()
  await loadTableData()
  
  nextTick(() => {
    initTrendChart()
    initStatusChart()
    initTimeChart()
    initHotDishesChart()
  })
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (statusChart) {
    statusChart.dispose()
    statusChart = null
  }
  if (timeChart) {
    timeChart.dispose()
    timeChart = null
  }
  if (hotDishesChart) {
    hotDishesChart.dispose()
    hotDishesChart = null
  }
})
</script>

<style scoped lang="scss">
.order-statistics {
  @apply p-6 bg-gray-50 min-h-screen;
}

.stats-cards {
  @apply mb-6;
}

.charts-section {
  @apply mb-6;
}

.analysis-section {
  @apply mb-6;
}

.chart-card,
.data-card {
  @apply bg-white rounded-lg shadow-sm p-6 h-full;
}

.chart-header,
.card-header {
  @apply flex justify-between items-center mb-4;
  
  h3 {
    @apply text-lg font-semibold text-gray-900;
  }
  
  .chart-controls {
    @apply flex items-center space-x-3;
  }
}

.chart-container {
  @apply h-80;
}

.table-section {
  @apply bg-white rounded-lg shadow-sm;
}

.revenue-text {
  @apply text-green-600 font-semibold;
}

.avg-value {
  @apply text-blue-600 font-medium;
}
</style>
