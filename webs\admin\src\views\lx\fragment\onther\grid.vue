<template>
  <div>
    <div v-if="false">
      <div class="ruler" />
      <div class="grid_container">
        <div class="cell-1" />
        <div class="cell-2" />
      </div>
    </div>
    <!--    网格布局动画-->
    <div class="container">
      <div class="item" />
      <div class="item" />
      <div class="item" />
      <div class="item" />
      <div class="item" />
      <div class="item" />
      <div class="item" />
      <div class="item" />
      <div class="item" />
    </div>
    <!--    网格布局旋转中的视差效果 -->
    <div class="box">
      <div class="item">
        <img src="https://s1.ax1x.com/2023/05/01/p98xG1U.jpg" alt="" />
      </div>
      <div class="item">
        <img src="https://s1.ax1x.com/2023/05/01/p98xUB9.jpg" alt="" />
      </div>
      <div class="item">
        <img src="https://s1.ax1x.com/2023/05/01/p98xG1U.jpg" alt="" />
      </div>
      <div class="item">
        <img src="https://s1.ax1x.com/2023/05/01/p98xUB9.jpg" alt="" />
      </div>
      <div class="item">
        <img src="https://s1.ax1x.com/2023/05/01/p98xG1U.jpg" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Grid"
});
</script>
<style scoped lang="scss">
//学习grid
.ruler {
  position: absolute;
  top: 0;
  left: 0;
  width: 580px;
  height: 580px;
  //background-image: url("5x5背景图 还是看浏览器样式叭");
  background-size: 580px 580px;
}
.grid_container {
  position: relative;
  display: grid;
  width: 500px;
  height: 500px;
  background-color: #eee;
  //5x5方格
  //grid-template-rows: 100px 100px 100px 100px 100px; //先写row 垂直方向分配 行
  //grid-template-columns: 100px 100px 100px 100px 100px; //再写columns 有简写方法
  //命名写法
  grid-template-rows: [y1] 100px [y2] 100px [y3] 100px [y4] 100px [y5] 100px; //先写row 垂直方向分配 行
  grid-template-columns: [x1] 100px [x2] 100px [x3] 100px [x4] 100px [x5] 100px; //再写columns 有简写方法
  //占比写法
  //grid-template-rows: 1fr repeat(4, 1fr); //repeat不适用于grid-template-areas
  //grid-template-columns: repeat(5, 1fr);
  //命名
  grid-template-areas:
    "h h h h h "
    "n m m m m"
    "n m m m m"
    ". f f f .";
  .cell-1 {
    background-color: pink;
    //grid-row: 1/3; //1-3占
    //grid-row: 2 / span 3; //2开始延申到3
    //grid-row: y1/y3; //1-3占
    //grid-column: 1/3;
    //grid-column: x1/x3;
    grid-area: n; //配合grid-template-areas
  }
  .cell-2 {
    background-color: yellow;
    //grid-row: 4/6;
    //grid-column: 1/3;
    grid-area: h;
  }
}
//网格布局的动画
.container {
  width: 400px;
  height: 400px;
  margin: 50px auto 0;
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  @for $i from 0 to 9 {
    .item:nth-child(#{$i + 1}) {
      $o: calc($i * 40%);
      background: hsl($o, 100%, 74%);
    }
    &:has(.item:nth-child(#{$i + 1}):hover) {
      //选择器has 某个条件成立就执行
      $r: floor($i/3 + 1); //1-3
      $c: $i % 3 +1; //1-3
      $arr: 1fr 1fr 1fr;
      $rows: set-nth(
        $arr,
        $r,
        2fr
      ); //使用 set-nth 函数，将 $arr 中的第 $r 项替换为 2fr，将 $arr 中的第 $c 项替换为 2fr。
      $columns: set-nth($arr, $c, 2fr);
      grid-template-columns: $columns;
      grid-template-rows: $rows;
    }
  }
}
//box
.box {
  border: 1px solid red;
  width: 300px;
  height: 300px;
  margin: 100px auto 0;
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  grid-template-columns: repeat(3, 1fr);
  grid-template:
    "a a b"
    "c d b"
    "c e e";
  gap: 10px;
  --r: 360deg;
  animation: rotation 10s linear infinite;
  .item {
    overflow: hidden;
    border: 2px solid slategray;
    display: flex;
    align-items: center;
    justify-content: center;
    &:nth-child(1) {
      grid-area: a;
    }
    &:nth-child(2) {
      grid-area: b;
    }
    &:nth-child(3) {
      grid-area: c;
    }
    &:nth-child(4) {
      grid-area: d;
    }
    &:nth-child(5) {
      grid-area: e;
    }
    img {
      max-width: 260% !important; //足够大 然后反向旋转
      height: 260% !important;
      --r: -360deg; //技巧
      object-fit: cover;
      animation: rotation 10s linear infinite;
    }
  }

  @keyframes rotation {
    to {
      transform: rotate(var(--r));
    }
  }
}
</style>
