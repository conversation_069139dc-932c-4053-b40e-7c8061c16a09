#!/usr/bin/env node

/**
 * 修复小程序 SCSS 语法错误
 * 将 @extend 改为 @include，修复语法错误
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'pages/home/<USER>',
  'pages/order/index.scss',
  'pages/today_order/index.scss',
  'pages/statistics/index.scss',
  'pages/message/index.scss',
  'pages/detail/index.scss',
  'pages/history_menu/index.scss',
  'pages/mine/index.scss'
];

// 语法修复规则
const syntaxFixes = [
  // 修复 @extend 为 @include
  {
    from: /@extend\s+\.([a-zA-Z0-9-_]+);/g,
    to: '@include $1;'
  },
  
  // 修复多个 @extend 在一行的情况
  {
    from: /@extend\s+\.([a-zA-Z0-9-_]+);\s*@extend\s+\.([a-zA-Z0-9-_]+);/g,
    to: '@include $1;\n  @include $2;'
  },
  
  // 修复错误的语法如 background-@extend
  {
    from: /background-@extend\s+\.([a-zA-Z0-9-_]+);/g,
    to: 'background: $white;'
  },
  
  // 修复 @extend .modern-btn; @extend .btn-primary; 格式
  {
    from: /@extend\s+\.modern-btn;\s*@extend\s+\.btn-primary;/g,
    to: '@include modern-btn;\n  @include btn-primary;'
  },
  
  {
    from: /@extend\s+\.modern-btn;\s*@extend\s+\.btn-secondary;/g,
    to: '@include modern-btn;\n  @include btn-secondary;'
  },
  
  {
    from: /@extend\s+\.modern-btn;\s*@extend\s+\.btn-success;/g,
    to: '@include modern-btn;\n  @include btn-success;'
  },
  
  {
    from: /@extend\s+\.modern-card;\s*@extend\s+\.card-primary;/g,
    to: '@include modern-card;\n  @include card-primary;'
  },
  
  // 修复导入语句
  {
    from: /@import\s+"\.\.\/\.\.\/styles\/modern-design\.scss";/g,
    to: '@import "../../styles/miniprogram-design.scss";'
  },
  
  // 修复类名映射
  {
    from: /@include\s+page-container;/g,
    to: '@include page-container;'
  },
  
  {
    from: /@include\s+page-container-safe;/g,
    to: '@include page-container-safe;'
  },
  
  // 修复颜色变量
  {
    from: /#6366f1/g,
    to: '$primary-solid'
  },
  
  {
    from: /#3b82f6/g,
    to: '$primary-solid'
  },
  
  {
    from: /#f59e0b/g,
    to: '$secondary'
  },
  
  {
    from: /#10b981/g,
    to: '$success'
  },
  
  {
    from: /#ef4444/g,
    to: '$error'
  },
  
  {
    from: /#ffffff/g,
    to: '$white'
  },
  
  {
    from: /#f8fafc/g,
    to: '$gray-50'
  },
  
  {
    from: /#f1f5f9/g,
    to: '$gray-100'
  },
  
  {
    from: /#94a3b8/g,
    to: '$gray-400'
  },
  
  {
    from: /#64748b/g,
    to: '$gray-500'
  },
  
  {
    from: /#475569/g,
    to: '$gray-600'
  },
  
  {
    from: /#334155/g,
    to: '$gray-700'
  },
  
  {
    from: /#0f172a/g,
    to: '$gray-900'
  }
];

/**
 * 修复单个文件
 * @param {string} filePath 文件路径
 */
function fixFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let changeCount = 0;

    // 应用所有语法修复
    syntaxFixes.forEach((fix, index) => {
      const beforeContent = content;
      content = content.replace(fix.from, fix.to);
      if (content !== beforeContent) {
        hasChanges = true;
        changeCount++;
        console.log(`  ✅ 应用修复规则 ${index + 1}: ${fix.from.toString().substring(0, 30)}...`);
      }
    });

    // 特殊处理：确保导入语句正确
    if (!content.includes('@import "../../styles/miniprogram-design.scss"') && 
        content.includes('@include')) {
      content = `@import "../../styles/miniprogram-design.scss";\n\n${content}`;
      hasChanges = true;
      changeCount++;
      console.log(`  ✅ 添加导入语句`);
    }

    // 移除重复的导入
    const importLines = content.match(/@import\s+"[^"]*";/g);
    if (importLines && importLines.length > 1) {
      // 保留第一个导入，移除重复的
      const firstImport = importLines[0];
      content = content.replace(/@import\s+"[^"]*";/g, '');
      content = `${firstImport}\n\n${content}`;
      hasChanges = true;
      changeCount++;
      console.log(`  ✅ 移除重复导入`);
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath} (${changeCount} 个修改)`);
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 修复文件失败 ${filePath}:`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复 SCSS 语法错误...\n');

  filesToFix.forEach(filePath => {
    console.log(`\n📁 修复文件: ${filePath}`);
    fixFile(filePath);
  });

  console.log('\n🎉 SCSS 语法修复完成！');
  console.log('\n📋 修复说明:');
  console.log('- 将 @extend 改为 @include');
  console.log('- 修复语法错误');
  console.log('- 统一导入语句');
  console.log('- 替换颜色变量');
  console.log('- 建议在微信开发者工具中测试编译');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  fixFile,
  syntaxFixes
};
