// 今日订单页面测试
const {
  createMockPage,
  waitFor,
  createMockEvent,
  clearAllMocks,
  mockStorage
} = require('../utils/testHelpers');

// 模拟 API 服务
jest.mock('../../services/api', () => ({
  orderApi: {
    createOrder: jest.fn()
  },
  userApi: {
    getFamilyMembers: jest.fn()
  }
}));

const { orderApi, userApi } = require('../../services/api');

describe('今日订单页面测试', () => {
  let todayOrderPage;
  
  // 模拟今日订单页面配置
  const todayOrderPageOptions = {
    data: {
      basketItems: [],
      showDialog: false,
      showUserSelector: false,
      orderSubmitted: false,
      remark: '',
      selectedTime: '',
      selectedUser: null,
      familyMembers: [],
      timeArray: [
        ['今天', '明天', '后天'],
        ['早餐', '午餐', '晚餐'],
        ['06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00']
      ],
      timeIndex: [0, 2, 6]
    },
    
    onLoad() {
      this.initTimeSelector();
      this.loadFamilyMembers();
    },
    
    onShow() {
      this.loadBasketData();
      const orderSubmitted = wx.getStorageSync('orderSubmitted') || false;
      this.setData({orderSubmitted});
    },
    
    async loadFamilyMembers() {
      try {
        const result = await userApi.getFamilyMembers();
        if (result.code === 200) {
          const currentUser = wx.getStorageSync('userInfo');
          const familyMembers = result.data || [];
          
          const selectedUser = familyMembers.find(member => member.id === currentUser.id) || familyMembers[0];
          
          this.setData({
            familyMembers,
            selectedUser
          });
        }
      } catch (error) {
        console.error('加载家庭成员失败:', error);
        const currentUser = wx.getStorageSync('userInfo');
        this.setData({
          familyMembers: [currentUser],
          selectedUser: currentUser
        });
      }
    },
    
    initTimeSelector() {
      const now = new Date();
      const hour = now.getHours();
      let timeIndex = this.data.timeIndex;
      
      if (hour < 10) {
        timeIndex = [0, 0, Math.max(hour - 6, 0)];
      } else if (hour < 16) {
        timeIndex = [0, 1, Math.max(hour - 6, 0)];
      } else {
        timeIndex = [0, 2, Math.max(hour - 6, 0)];
      }
      
      this.setData({timeIndex});
      this.updateSelectedTime(timeIndex);
    },
    
    updateSelectedTime(timeIndex) {
      const {timeArray} = this.data;
      const selectedTime = `${timeArray[0][timeIndex[0]]} ${timeArray[1][timeIndex[1]]} ${timeArray[2][timeIndex[2]]}`;
      this.setData({selectedTime});
    },
    
    loadBasketData() {
      const basket = wx.getStorageSync('basket') || {};
      const basketItems = Object.values(basket);
      this.setData({basketItems});
    },
    
    showUserSelector() {
      this.setData({
        showUserSelector: true
      });
    },
    
    hideUserSelector() {
      this.setData({
        showUserSelector: false
      });
    },
    
    selectUser(e) {
      const userId = e.currentTarget.dataset.userId;
      const selectedUser = this.data.familyMembers.find(member => member.id === userId);
      
      this.setData({
        selectedUser,
        showUserSelector: false
      });
    },
    
    submitOrder() {
      if (this.data.basketItems.length === 0) {
        wx.showToast({
          title: '菜单为空',
          icon: 'none'
        });
        return;
      }
      
      if (!this.data.selectedUser) {
        wx.showToast({
          title: '请选择用餐人员',
          icon: 'none'
        });
        return;
      }
      
      this.setData({
        showDialog: true
      });
    },
    
    async handleConfirm() {
      const {basketItems, remark, selectedTime, selectedUser} = this.data;
      
      try {
        wx.showLoading({title: '提交中...'});
        
        const orderData = {
          items: basketItems.map(item => ({
            dishId: item.id,
            dishName: item.name,
            count: item.count
          })),
          remark: remark || '',
          diningTime: this.formatDiningTime(selectedTime),
          userId: selectedUser.id,
          userName: selectedUser.name
        };
        
        const result = await orderApi.createOrder(orderData);
        
        if (result.code === 200) {
          const todayMenu = {
            id: result.data.id,
            date: selectedTime || '今日',
            dishes: basketItems,
            remark: remark,
            createdAt: new Date().toISOString(),
            status: 'pending'
          };
          
          let historyMenus = wx.getStorageSync('historyMenus') || [];
          historyMenus.unshift(todayMenu);
          
          if (historyMenus.length > 10) {
            historyMenus = historyMenus.slice(0, 10);
          }
          
          wx.setStorageSync('historyMenus', historyMenus);
          wx.setStorageSync('todayMenu', todayMenu);
          wx.setStorageSync('basket', {});
          wx.setStorageSync('orderSubmitted', true);
          
          this.setData({
            showDialog: false,
            basketItems: [],
            orderSubmitted: true
          });
          
          wx.showToast({
            title: '订单提交成功',
            icon: 'success'
          });
        } else {
          throw new Error(result.message || '提交失败');
        }
      } catch (error) {
        console.error('提交订单失败:', error);
        wx.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none'
        });
      } finally {
        wx.hideLoading();
      }
    },
    
    formatDiningTime(selectedTime) {
      if (!selectedTime) {
        return new Date().toISOString();
      }
      
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      const parts = selectedTime.split(' ');
      const dayOffset = parts[0] === '今天' ? 0 : parts[0] === '明天' ? 1 : 2;
      const timeStr = parts[2] || '18:00';
      const [hour, minute] = timeStr.split(':').map(Number);
      
      const diningDate = new Date(today);
      diningDate.setDate(diningDate.getDate() + dayOffset);
      diningDate.setHours(hour, minute, 0, 0);
      
      return diningDate.toISOString();
    },
    
    handleCancel() {
      this.setData({
        showDialog: false
      });
    }
  };
  
  beforeEach(() => {
    clearAllMocks();
    todayOrderPage = createMockPage(todayOrderPageOptions);
  });
  
  describe('页面初始化', () => {
    test('应该正确初始化页面数据', () => {
      expect(todayOrderPage.data.basketItems).toEqual([]);
      expect(todayOrderPage.data.showDialog).toBe(false);
      expect(todayOrderPage.data.showUserSelector).toBe(false);
      expect(todayOrderPage.data.orderSubmitted).toBe(false);
    });
    
    test('应该加载家庭成员列表', async () => {
      const mockMembers = [
        { id: 1, name: '张三', avatar: 'avatar1.jpg' },
        { id: 2, name: '李四', avatar: 'avatar2.jpg' }
      ];
      
      const mockCurrentUser = { id: 1, name: '张三' };
      
      userApi.getFamilyMembers.mockResolvedValue({
        code: 200,
        data: mockMembers
      });
      
      wx.getStorageSync.mockImplementation((key) => {
        if (key === 'userInfo') return mockCurrentUser;
        return '';
      });
      
      await todayOrderPage.loadFamilyMembers();
      await waitFor(50);
      
      expect(userApi.getFamilyMembers).toHaveBeenCalled();
      expect(todayOrderPage.data.familyMembers).toEqual(mockMembers);
      expect(todayOrderPage.data.selectedUser).toEqual(mockCurrentUser);
    });
    
    test('加载家庭成员失败应该使用当前用户', async () => {
      const mockCurrentUser = { id: 1, name: '张三' };
      
      userApi.getFamilyMembers.mockRejectedValue(new Error('网络错误'));
      
      wx.getStorageSync.mockImplementation((key) => {
        if (key === 'userInfo') return mockCurrentUser;
        return '';
      });
      
      await todayOrderPage.loadFamilyMembers();
      await waitFor(50);
      
      expect(todayOrderPage.data.familyMembers).toEqual([mockCurrentUser]);
      expect(todayOrderPage.data.selectedUser).toEqual(mockCurrentUser);
    });
  });
  
  describe('用户选择功能', () => {
    beforeEach(() => {
      const mockMembers = [
        { id: 1, name: '张三', avatar: 'avatar1.jpg' },
        { id: 2, name: '李四', avatar: 'avatar2.jpg' }
      ];
      
      todayOrderPage.setData({
        familyMembers: mockMembers,
        selectedUser: mockMembers[0]
      });
    });
    
    test('应该能够显示用户选择器', () => {
      todayOrderPage.showUserSelector();
      
      expect(todayOrderPage.data.showUserSelector).toBe(true);
    });
    
    test('应该能够隐藏用户选择器', () => {
      todayOrderPage.setData({ showUserSelector: true });
      todayOrderPage.hideUserSelector();
      
      expect(todayOrderPage.data.showUserSelector).toBe(false);
    });
    
    test('应该能够选择用户', () => {
      const event = createMockEvent({ userId: '2' });
      todayOrderPage.selectUser(event);
      
      expect(todayOrderPage.data.selectedUser.id).toBe('2');
      expect(todayOrderPage.data.selectedUser.name).toBe('李四');
      expect(todayOrderPage.data.showUserSelector).toBe(false);
    });
  });
  
  describe('订单提交', () => {
    beforeEach(() => {
      const mockUser = { id: 1, name: '张三' };
      const mockBasketItems = [
        { id: 1, name: '宫保鸡丁', count: 2 },
        { id: 2, name: '麻婆豆腐', count: 1 }
      ];
      
      todayOrderPage.setData({
        selectedUser: mockUser,
        basketItems: mockBasketItems,
        selectedTime: '今天 晚餐 18:00',
        remark: '少辣'
      });
    });
    
    test('空菜单应该显示错误提示', () => {
      todayOrderPage.setData({ basketItems: [] });
      todayOrderPage.submitOrder();
      
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '菜单为空',
        icon: 'none'
      });
      expect(todayOrderPage.data.showDialog).toBe(false);
    });
    
    test('未选择用户应该显示错误提示', () => {
      todayOrderPage.setData({ selectedUser: null });
      todayOrderPage.submitOrder();
      
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '请选择用餐人员',
        icon: 'none'
      });
      expect(todayOrderPage.data.showDialog).toBe(false);
    });
    
    test('有效订单应该显示确认对话框', () => {
      todayOrderPage.submitOrder();
      
      expect(todayOrderPage.data.showDialog).toBe(true);
    });
    
    test('应该能够成功提交订单', async () => {
      orderApi.createOrder.mockResolvedValue({
        code: 200,
        data: { id: 123 }
      });
      
      await todayOrderPage.handleConfirm();
      await waitFor(50);
      
      expect(orderApi.createOrder).toHaveBeenCalledWith({
        items: [
          { dishId: 1, dishName: '宫保鸡丁', count: 2 },
          { dishId: 2, dishName: '麻婆豆腐', count: 1 }
        ],
        remark: '少辣',
        diningTime: expect.any(String),
        userId: 1,
        userName: '张三'
      });
      
      expect(wx.setStorageSync).toHaveBeenCalledWith('basket', {});
      expect(wx.setStorageSync).toHaveBeenCalledWith('orderSubmitted', true);
      expect(todayOrderPage.data.orderSubmitted).toBe(true);
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '订单提交成功',
        icon: 'success'
      });
    });
    
    test('提交订单失败应该显示错误', async () => {
      orderApi.createOrder.mockRejectedValue(new Error('网络错误'));
      
      await todayOrderPage.handleConfirm();
      await waitFor(50);
      
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '网络错误',
        icon: 'none'
      });
      expect(todayOrderPage.data.orderSubmitted).toBe(false);
    });
    
    test('应该能够取消订单提交', () => {
      todayOrderPage.setData({ showDialog: true });
      todayOrderPage.handleCancel();
      
      expect(todayOrderPage.data.showDialog).toBe(false);
    });
  });
  
  describe('时间格式化', () => {
    test('应该正确格式化今天的时间', () => {
      const result = todayOrderPage.formatDiningTime('今天 晚餐 18:00');
      const date = new Date(result);
      
      expect(date.getHours()).toBe(18);
      expect(date.getMinutes()).toBe(0);
    });
    
    test('应该正确格式化明天的时间', () => {
      const result = todayOrderPage.formatDiningTime('明天 早餐 08:00');
      const date = new Date(result);
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      expect(date.getDate()).toBe(tomorrow.getDate());
      expect(date.getHours()).toBe(8);
    });
    
    test('空时间应该返回当前时间', () => {
      const result = todayOrderPage.formatDiningTime('');
      const date = new Date(result);
      const now = new Date();
      
      expect(Math.abs(date.getTime() - now.getTime())).toBeLessThan(1000);
    });
  });
});
