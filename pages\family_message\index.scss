.container {
  background: #121212;
  min-height: 100vh;
  padding: 24rpx 20rpx;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #00f2ea;
  text-align: center;
}

.main-card {
  background: linear-gradient(120deg, #23272f 60%, #23233b 100%);
  border-radius: 36rpx;
  box-shadow: 0 8rpx 36rpx rgba(0, 242, 234, 0.13);
  margin-bottom: 56rpx;
  padding: 48rpx 36rpx;
  border: 3rpx solid #232323;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
    border-radius: 44rpx 44rpx 0 0;
    opacity: 0.8;
  }
}

.msg-item {
  background: linear-gradient(120deg, #232323 60%, #2a2a2a 100%);
  border-radius: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.13);
  padding: 28rpx 28rpx 20rpx 28rpx;
  margin-bottom: 24rpx;
  border: 2.4rpx solid #232323;
}

.msg-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.msg-user {
  font-weight: bold;
  margin-right: 12rpx;
  font-size: 30rpx;

  &.blue {
    color: #00f2ea;
  }

  &.pink {
    color: #fe2c55;
  }
}

.msg-content {
  color: #fff;
  font-size: 30rpx;
}

.msg-time {
  color: #e6e6e6;
  font-size: 24rpx;
  margin-top: 4rpx;
}

.msg-form {
  display: flex;
  margin-top: 20rpx;
}

.msg-input {
  flex: 1;
  border: 2rpx solid #232323;
  border-radius: 24rpx 0 0 24rpx;
  padding: 0 28rpx;
  font-size: 30rpx;
  background: #2a2a2a;
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  box-sizing: border-box;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
  line-height: 88rpx;
}

.msg-send {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  border-radius: 0 24rpx 24rpx 0;
  font-size: 36rpx;
  background: #00f2ea;
  color: #181a20;
}

.send-icon {
  font-size: 40rpx !important;
  font-weight: bold;
  color: white !important;
}
