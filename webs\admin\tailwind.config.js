/** @type {import('tailwindcss').Config} */
const defaultTheme = require("tailwindcss/defaultTheme");

const spacing = Array.from({ length: 1500 }).reduce((map, _, index) => {
  map[index] = `${index}px`;
  return map;
}, {});

const opacity = Array.from({ length: 8 }).reduce((map, _, index) => {
  map[index + 1] = `${(index + 1) / 100}`;
  return map;
}, {});

const getNumSpacing = num =>
  Object.keys(spacing)
    .slice(0, num)
    .reduce((map, key) => {
      map[key] = spacing[key];
      return map;
    }, {});

module.exports = {
  darkMode: "class",
  corePlugins: {
    preflight: false
  },
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      spacing,
      extend: {
        fontSize: ({ theme }) => ({
          ...theme("spacing")
        }),
        colors: {
          bg_color: "var(--el-bg-color)",
          primary: "var(--el-color-primary)",
          text_color_primary: "var(--el-text-color-primary)",
          text_color_regular: "var(--el-text-color-regular)"
        },
        fontWeight: {
          400: "400",
          500: "500",
          600: "600"
        },
        borderRadius: getNumSpacing(20), // 取前 20 个值
        opacity, // 0.01 - 0.09 默认透明度为1
        lineHeight: getNumSpacing(25)
      },
      fontFamily: {
        sans: ["PingFang SC", ...defaultTheme.fontFamily.sans]
      }
    }
  }
};
