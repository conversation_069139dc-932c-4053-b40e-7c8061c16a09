#!/usr/bin/env node

/**
 * 修复小程序中的 CSS 变量问题
 * 将所有 var(--variable) 替换为实际的颜色值
 */

const fs = require('fs');
const path = require('path');

// CSS 变量映射表
const cssVariables = {
  // 主色调 - 蓝色系
  '--primary-50': '#eff6ff',
  '--primary-100': '#dbeafe',
  '--primary-200': '#bfdbfe',
  '--primary-300': '#93c5fd',
  '--primary-400': '#60a5fa',
  '--primary-500': '#3b82f6',
  '--primary-600': '#2563eb',
  '--primary-700': '#1d4ed8',
  '--primary-800': '#1e40af',
  '--primary-900': '#1e3a8a',

  // 粉色系
  '--pink-50': '#fdf2f8',
  '--pink-100': '#fce7f3',
  '--pink-200': '#fbcfe8',
  '--pink-300': '#f9a8d4',
  '--pink-400': '#f472b6',
  '--pink-500': '#ec4899',
  '--pink-600': '#db2777',
  '--pink-700': '#be185d',
  '--pink-800': '#9d174d',
  '--pink-900': '#831843',

  // 灰色系
  '--gray-50': '#f9fafb',
  '--gray-100': '#f3f4f6',
  '--gray-200': '#e5e7eb',
  '--gray-300': '#d1d5db',
  '--gray-400': '#9ca3af',
  '--gray-500': '#6b7280',
  '--gray-600': '#4b5563',
  '--gray-700': '#374151',
  '--gray-800': '#1f2937',
  '--gray-900': '#111827',

  // 其他颜色
  '--green-500': '#10b981',
  '--green-600': '#059669',
  '--orange-500': '#f97316',
  '--orange-600': '#ea580c',
  '--yellow-500': '#f59e0b',
  '--yellow-600': '#d97706',
  '--red-500': '#ef4444',
  '--red-600': '#dc2626',

  // 阴影
  '--shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  '--shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  '--shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  '--shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  '--shadow-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '--shadow-2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
};

// 需要处理的文件列表
const filesToProcess = [
  'pages/home/<USER>',
  'pages/order/index.scss',
  'pages/today_order/index.scss',
  'pages/add_menu/index.scss',
  'pages/statistics/index.scss',
  'pages/message/index.scss',
  'pages/detail/index.scss',
  'pages/history_menu/index.scss',
  'pages/mine/index.scss',
  'components/refresh-list/refresh-list.scss'
];

/**
 * 替换文件中的 CSS 变量
 * @param {string} filePath 文件路径
 */
function replaceCssVariables(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 替换所有 var(--variable) 为实际值
    Object.keys(cssVariables).forEach(variable => {
      const regex = new RegExp(`var\\(${variable.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}\\)`, 'g');
      if (content.includes(`var(${variable})`)) {
        content = content.replace(regex, cssVariables[variable]);
        hasChanges = true;
        console.log(`  ✅ 替换 var(${variable}) -> ${cssVariables[variable]}`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
    } else {
      console.log(`ℹ️  无需更新: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复 CSS 变量问题...\n');

  filesToProcess.forEach(filePath => {
    console.log(`\n📁 处理文件: ${filePath}`);
    replaceCssVariables(filePath);
  });

  console.log('\n🎉 CSS 变量修复完成！');
  console.log('\n📋 修复说明:');
  console.log('- 所有 var(--variable) 已替换为实际颜色值');
  console.log('- 小程序现在可以正确显示样式');
  console.log('- 建议在微信开发者工具中重新编译项目');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  replaceCssVariables,
  cssVariables
};
