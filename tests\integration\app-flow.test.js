// 小程序整体流程集成测试
const {
  createMockPage,
  waitFor,
  createMockEvent,
  createMockInputEvent,
  clearAllMocks,
  mockStorage
} = require('../utils/testHelpers');

// 模拟所有 API 服务
jest.mock('../../services/api', () => ({
  userApi: {
    login: jest.fn(),
    register: jest.fn(),
    getFamilyMembers: jest.fn()
  },
  dishApi: {
    getDishesByCategory: jest.fn(),
    getDishDetail: jest.fn(),
    createDish: jest.fn()
  },
  orderApi: {
    createOrder: jest.fn(),
    getOrders: jest.fn()
  },
  menuApi: {
    getStatistics: jest.fn()
  },
  messageApi: {
    getMessages: jest.fn(),
    createMessage: jest.fn()
  }
}));

const { userApi, dishApi, orderApi, menuApi, messageApi } = require('../../services/api');

describe('小程序完整功能流程测试', () => {
  beforeEach(() => {
    clearAllMocks();
  });

  describe('用户登录流程', () => {
    test('完整的登录到点菜流程', async () => {
      // 1. 用户登录
      const loginPage = createMockPage({
        data: {
          loginType: 'password',
          username: '',
          password: '',
          loading: false,
          loginTip: ''
        },
        
        async loginWithPassword() {
          const { username, password } = this.data;
          
          if (!username || !password) {
            this.setData({ loginTip: '请输入用户名和密码' });
            return;
          }
          
          this.setData({ loading: true });
          
          try {
            const result = await userApi.login({ username, password });
            
            if (result.code === 200) {
              wx.setStorageSync('token', result.data.token);
              wx.setStorageSync('userInfo', result.data.userInfo);
              
              wx.switchTab({ url: '/pages/home/<USER>' });
            }
          } catch (error) {
            this.setData({ loginTip: '登录失败' });
          } finally {
            this.setData({ loading: false });
          }
        }
      });
      
      // 模拟用户输入
      loginPage.setData({
        username: 'testuser',
        password: 'password123'
      });
      
      // 模拟登录成功
      userApi.login.mockResolvedValue({
        code: 200,
        data: {
          token: 'mock_token',
          userInfo: { id: 1, name: '测试用户' }
        }
      });
      
      await loginPage.loginWithPassword();
      await waitFor(50);
      
      expect(userApi.login).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123'
      });
      expect(wx.setStorageSync).toHaveBeenCalledWith('token', 'mock_token');
      expect(wx.switchTab).toHaveBeenCalledWith({ url: '/pages/home/<USER>' });
    });
  });

  describe('点菜到下单流程', () => {
    test('完整的点菜到提交订单流程', async () => {
      // 1. 加载菜品数据
      const orderPage = createMockPage({
        data: {
          currentType: 'hot',
          basketCount: 0,
          foodData: {},
          foodList: []
        },
        
        async loadDishes() {
          const result = await dishApi.getDishesByCategory();
          if (result.code === 200) {
            this.setData({
              foodData: result.data,
              loading: false
            });
          }
        },
        
        addToBasket(e) {
          const { id, name, img } = e.currentTarget.dataset;
          let basket = wx.getStorageSync('basket') || {};
          
          if (basket[id]) {
            basket[id].count += 1;
          } else {
            basket[id] = { id, name, img, count: 1 };
          }
          
          wx.setStorageSync('basket', basket);
          this.updateBasketCount();
        },
        
        updateBasketCount() {
          const basket = wx.getStorageSync('basket') || {};
          const total = Object.values(basket).reduce((sum, item) => sum + item.count, 0);
          this.setData({ basketCount: total });
        }
      });
      
      // 模拟菜品数据
      const mockDishes = {
        hot: [
          { id: 1, name: '宫保鸡丁', img: 'test1.jpg' },
          { id: 2, name: '麻婆豆腐', img: 'test2.jpg' }
        ]
      };
      
      dishApi.getDishesByCategory.mockResolvedValue({
        code: 200,
        data: mockDishes
      });
      
      // 模拟空购物篮
      wx.getStorageSync.mockReturnValue({});
      
      // 加载菜品
      await orderPage.loadDishes();
      await waitFor(50);
      
      expect(orderPage.data.foodData).toEqual(mockDishes);
      
      // 添加菜品到购物篮
      const addEvent = createMockEvent({
        id: '1',
        name: '宫保鸡丁',
        img: 'test1.jpg'
      });
      
      orderPage.addToBasket(addEvent);
      
      expect(wx.setStorageSync).toHaveBeenCalledWith('basket', {
        '1': { id: '1', name: '宫保鸡丁', img: 'test1.jpg', count: 1 }
      });
      expect(orderPage.data.basketCount).toBe(1);
      
      // 2. 提交订单
      const todayOrderPage = createMockPage({
        data: {
          basketItems: [],
          selectedUser: { id: 1, name: '测试用户' },
          selectedTime: '今天 晚餐 18:00',
          remark: ''
        },
        
        loadBasketData() {
          const basket = wx.getStorageSync('basket') || {};
          const basketItems = Object.values(basket);
          this.setData({ basketItems });
        },
        
        async handleConfirm() {
          const { basketItems, selectedUser, selectedTime, remark } = this.data;
          
          const orderData = {
            items: basketItems.map(item => ({
              dishId: item.id,
              dishName: item.name,
              count: item.count
            })),
            userId: selectedUser.id,
            userName: selectedUser.name,
            diningTime: selectedTime,
            remark
          };
          
          const result = await orderApi.createOrder(orderData);
          
          if (result.code === 200) {
            wx.setStorageSync('basket', {});
            wx.setStorageSync('orderSubmitted', true);
            this.setData({ orderSubmitted: true });
          }
        }
      });
      
      // 模拟购物篮数据
      wx.getStorageSync.mockReturnValue({
        '1': { id: 1, name: '宫保鸡丁', img: 'test1.jpg', count: 1 }
      });
      
      todayOrderPage.loadBasketData();
      
      expect(todayOrderPage.data.basketItems).toHaveLength(1);
      expect(todayOrderPage.data.basketItems[0].name).toBe('宫保鸡丁');
      
      // 模拟订单提交成功
      orderApi.createOrder.mockResolvedValue({
        code: 200,
        data: { id: 123 }
      });
      
      await todayOrderPage.handleConfirm();
      await waitFor(50);
      
      expect(orderApi.createOrder).toHaveBeenCalledWith({
        items: [{ dishId: 1, dishName: '宫保鸡丁', count: 1 }],
        userId: 1,
        userName: '测试用户',
        diningTime: '今天 晚餐 18:00',
        remark: ''
      });
      expect(wx.setStorageSync).toHaveBeenCalledWith('orderSubmitted', true);
    });
  });

  describe('添加菜品流程', () => {
    test('完整的添加菜品流程', async () => {
      const addMenuPage = createMockPage({
        data: {
          name: '',
          material: '',
          method: '',
          category: 'hot',
          tempImagePath: ''
        },
        
        async submitDish() {
          const { name, material, method, category, tempImagePath } = this.data;
          
          if (!name || !material || !method) {
            wx.showToast({ title: '请填写完整信息', icon: 'none' });
            return;
          }
          
          const dishData = {
            name: name.trim(),
            description: `${material.trim()}\n\n制作方法：\n${method.trim()}`,
            category: category,
            image: tempImagePath || 'default.jpg'
          };
          
          const result = await dishApi.createDish(dishData);
          
          if (result.code === 200) {
            wx.showToast({ title: '添加成功', icon: 'success' });
            wx.switchTab({ url: '/pages/order/index' });
          }
        }
      });
      
      // 设置菜品信息
      addMenuPage.setData({
        name: '新菜品',
        material: '鸡肉、花生米',
        method: '爆炒',
        category: 'hot',
        tempImagePath: 'new_dish.jpg'
      });
      
      // 模拟添加成功
      dishApi.createDish.mockResolvedValue({
        code: 200,
        data: { id: 456 }
      });
      
      await addMenuPage.submitDish();
      await waitFor(50);
      
      expect(dishApi.createDish).toHaveBeenCalledWith({
        name: '新菜品',
        description: '鸡肉、花生米\n\n制作方法：\n爆炒',
        category: 'hot',
        image: 'new_dish.jpg'
      });
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '添加成功',
        icon: 'success'
      });
      expect(wx.switchTab).toHaveBeenCalledWith({
        url: '/pages/order/index'
      });
    });
  });

  describe('统计页面流程', () => {
    test('应该正确加载和显示统计数据', async () => {
      const statisticsPage = createMockPage({
        data: {
          loading: true,
          statistics: {
            todayOrders: 0,
            totalDishes: 0,
            activeUsers: 0
          }
        },
        
        async loadAllStatistics() {
          try {
            const result = await menuApi.getStatistics();
            
            if (result.code === 200) {
              this.setData({
                statistics: result.data,
                loading: false
              });
            }
          } catch (error) {
            this.setData({ loading: false });
          }
        }
      });
      
      // 模拟统计数据
      const mockStats = {
        todayOrders: 15,
        totalDishes: 50,
        activeUsers: 8
      };
      
      menuApi.getStatistics.mockResolvedValue({
        code: 200,
        data: mockStats
      });
      
      await statisticsPage.loadAllStatistics();
      await waitFor(50);
      
      expect(menuApi.getStatistics).toHaveBeenCalled();
      expect(statisticsPage.data.statistics).toEqual(mockStats);
      expect(statisticsPage.data.loading).toBe(false);
    });
  });

  describe('消息功能流程', () => {
    test('应该能够发送和接收消息', async () => {
      const messagePage = createMockPage({
        data: {
          messages: [],
          newMessage: ''
        },
        
        async loadMessages() {
          const result = await messageApi.getMessages();
          if (result.code === 200) {
            this.setData({ messages: result.data });
          }
        },
        
        async sendMessage() {
          const { newMessage } = this.data;
          
          if (!newMessage.trim()) {
            wx.showToast({ title: '请输入消息内容', icon: 'none' });
            return;
          }
          
          const result = await messageApi.createMessage({
            content: newMessage.trim()
          });
          
          if (result.code === 200) {
            this.setData({ newMessage: '' });
            this.loadMessages();
          }
        }
      });
      
      // 模拟消息数据
      const mockMessages = [
        { id: 1, content: '今天想吃什么？', sender: '妈妈' },
        { id: 2, content: '宫保鸡丁吧', sender: '小明' }
      ];
      
      messageApi.getMessages.mockResolvedValue({
        code: 200,
        data: mockMessages
      });
      
      // 加载消息
      await messagePage.loadMessages();
      await waitFor(50);
      
      expect(messagePage.data.messages).toEqual(mockMessages);
      
      // 发送新消息
      messagePage.setData({ newMessage: '我也想吃' });
      
      messageApi.createMessage.mockResolvedValue({
        code: 200,
        data: { id: 3 }
      });
      
      await messagePage.sendMessage();
      await waitFor(50);
      
      expect(messageApi.createMessage).toHaveBeenCalledWith({
        content: '我也想吃'
      });
      expect(messagePage.data.newMessage).toBe('');
    });
  });

  describe('错误处理流程', () => {
    test('应该正确处理网络错误', async () => {
      const page = createMockPage({
        data: { loading: false },
        
        async loadData() {
          try {
            this.setData({ loading: true });
            await dishApi.getDishesByCategory();
          } catch (error) {
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
          } finally {
            this.setData({ loading: false });
          }
        }
      });
      
      // 模拟网络错误
      dishApi.getDishesByCategory.mockRejectedValue(new Error('网络错误'));
      
      await page.loadData();
      await waitFor(50);
      
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '网络错误，请重试',
        icon: 'none'
      });
      expect(page.data.loading).toBe(false);
    });
  });
});
