// 登录页面测试
const {
  createMockPage,
  mockRequest,
  mockRequestError,
  waitFor,
  createMockEvent,
  createMockInputEvent,
  mockStorage,
  clearAllMocks,
  expectPageData
} = require('../utils/testHelpers');

// 模拟 API 服务
jest.mock('../../services/api', () => ({
  userApi: {
    login: jest.fn(),
    register: jest.fn(),
    wechatLogin: jest.fn()
  }
}));

const { userApi } = require('../../services/api');

describe('登录页面测试', () => {
  let loginPage;
  
  // 模拟登录页面配置
  const loginPageOptions = {
    data: {
      loginType: 'password',
      username: '',
      password: '',
      loading: false,
      loginTip: '',
      rememberPwd: false,
      registerForm: {
        name: '',
        phone: '',
        password: '',
        confirmPassword: ''
      }
    },
    
    onLoad() {
      // 页面加载逻辑
    },
    
    switchLoginType(e) {
      const type = e.currentTarget.dataset.type;
      this.setData({
        loginType: type,
        loginTip: ''
      });
    },
    
    onUsernameInput(e) {
      this.setData({
        username: e.detail.value
      });
    },
    
    onPasswordInput(e) {
      this.setData({
        password: e.detail.value
      });
    },
    
    async loginWithPassword() {
      const { username, password } = this.data;
      
      if (!username || !password) {
        this.setData({
          loginTip: '请输入用户名和密码'
        });
        return;
      }
      
      this.setData({ loading: true });
      
      try {
        const result = await userApi.login({ username, password });
        
        if (result.code === 200) {
          wx.setStorageSync('token', result.data.token);
          wx.setStorageSync('userInfo', result.data.userInfo);
          
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/home/<USER>'
            });
          }, 1500);
        } else {
          this.setData({
            loginTip: result.message || '登录失败'
          });
        }
      } catch (error) {
        this.setData({
          loginTip: '网络错误，请重试'
        });
      } finally {
        this.setData({ loading: false });
      }
    },
    
    async registerWithPassword() {
      const { registerForm } = this.data;
      const { name, phone, password, confirmPassword } = registerForm;
      
      if (!name || !phone || !password || !confirmPassword) {
        this.setData({
          loginTip: '请填写完整信息'
        });
        return;
      }
      
      if (password !== confirmPassword) {
        this.setData({
          loginTip: '两次密码输入不一致'
        });
        return;
      }
      
      this.setData({ loading: true });
      
      try {
        const result = await userApi.register({
          name,
          phone,
          password
        });
        
        if (result.code === 200) {
          wx.showToast({
            title: '注册成功',
            icon: 'success'
          });
          
          this.setData({
            loginType: 'password',
            loginTip: ''
          });
        } else {
          this.setData({
            loginTip: result.message || '注册失败'
          });
        }
      } catch (error) {
        this.setData({
          loginTip: '网络错误，请重试'
        });
      } finally {
        this.setData({ loading: false });
      }
    },
    
    async loginWithWechat() {
      this.setData({ loading: true });
      
      try {
        const result = await userApi.wechatLogin();
        
        if (result.code === 200) {
          wx.setStorageSync('token', result.data.token);
          wx.setStorageSync('userInfo', result.data.userInfo);
          
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/home/<USER>'
            });
          }, 1500);
        } else {
          this.setData({
            loginTip: result.message || '微信登录失败'
          });
        }
      } catch (error) {
        this.setData({
          loginTip: '微信登录失败，请重试'
        });
      } finally {
        this.setData({ loading: false });
      }
    }
  };
  
  beforeEach(() => {
    clearAllMocks();
    loginPage = createMockPage(loginPageOptions);
  });
  
  describe('页面初始化', () => {
    test('应该正确初始化页面数据', () => {
      expect(loginPage.data.loginType).toBe('password');
      expect(loginPage.data.username).toBe('');
      expect(loginPage.data.password).toBe('');
      expect(loginPage.data.loading).toBe(false);
    });
  });
  
  describe('登录方式切换', () => {
    test('应该能够切换到注册模式', () => {
      const event = createMockEvent({ type: 'register' });
      loginPage.switchLoginType(event);
      
      expect(loginPage.data.loginType).toBe('register');
      expect(loginPage.data.loginTip).toBe('');
    });
    
    test('应该能够切换到微信登录模式', () => {
      const event = createMockEvent({ type: 'wechat' });
      loginPage.switchLoginType(event);
      
      expect(loginPage.data.loginType).toBe('wechat');
    });
  });
  
  describe('用户输入', () => {
    test('应该能够输入用户名', () => {
      const event = createMockInputEvent('testuser');
      loginPage.onUsernameInput(event);
      
      expect(loginPage.data.username).toBe('testuser');
    });
    
    test('应该能够输入密码', () => {
      const event = createMockInputEvent('password123');
      loginPage.onPasswordInput(event);
      
      expect(loginPage.data.password).toBe('password123');
    });
  });
  
  describe('密码登录', () => {
    test('空用户名或密码应该显示错误提示', async () => {
      await loginPage.loginWithPassword();
      
      expect(loginPage.data.loginTip).toBe('请输入用户名和密码');
      expect(loginPage.data.loading).toBe(false);
    });
    
    test('登录成功应该保存token并跳转', async () => {
      // 设置输入数据
      loginPage.setData({
        username: 'testuser',
        password: 'password123'
      });
      
      // 模拟API成功响应
      userApi.login.mockResolvedValue({
        code: 200,
        data: {
          token: 'mock_token',
          userInfo: { name: '测试用户' }
        }
      });
      
      await loginPage.loginWithPassword();
      await waitFor(50);
      
      expect(userApi.login).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123'
      });
      expect(wx.setStorageSync).toHaveBeenCalledWith('token', 'mock_token');
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '登录成功',
        icon: 'success'
      });
    });
    
    test('登录失败应该显示错误信息', async () => {
      loginPage.setData({
        username: 'testuser',
        password: 'wrongpassword'
      });
      
      userApi.login.mockResolvedValue({
        code: 400,
        message: '用户名或密码错误'
      });
      
      await loginPage.loginWithPassword();
      await waitFor(50);
      
      expect(loginPage.data.loginTip).toBe('用户名或密码错误');
      expect(loginPage.data.loading).toBe(false);
    });
  });
  
  describe('用户注册', () => {
    beforeEach(() => {
      loginPage.setData({ loginType: 'register' });
    });
    
    test('注册信息不完整应该显示错误', async () => {
      await loginPage.registerWithPassword();
      
      expect(loginPage.data.loginTip).toBe('请填写完整信息');
    });
    
    test('密码不一致应该显示错误', async () => {
      loginPage.setData({
        registerForm: {
          name: '测试用户',
          phone: '13800138000',
          password: 'password123',
          confirmPassword: 'password456'
        }
      });
      
      await loginPage.registerWithPassword();
      
      expect(loginPage.data.loginTip).toBe('两次密码输入不一致');
    });
    
    test('注册成功应该切换到登录模式', async () => {
      loginPage.setData({
        registerForm: {
          name: '测试用户',
          phone: '13800138000',
          password: 'password123',
          confirmPassword: 'password123'
        }
      });
      
      userApi.register.mockResolvedValue({
        code: 200,
        message: '注册成功'
      });
      
      await loginPage.registerWithPassword();
      await waitFor(50);
      
      expect(userApi.register).toHaveBeenCalledWith({
        name: '测试用户',
        phone: '13800138000',
        password: 'password123'
      });
      expect(loginPage.data.loginType).toBe('password');
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '注册成功',
        icon: 'success'
      });
    });
  });
  
  describe('微信登录', () => {
    test('微信登录成功应该保存token并跳转', async () => {
      userApi.wechatLogin.mockResolvedValue({
        code: 200,
        data: {
          token: 'wechat_token',
          userInfo: { name: '微信用户' }
        }
      });
      
      await loginPage.loginWithWechat();
      await waitFor(50);
      
      expect(wx.setStorageSync).toHaveBeenCalledWith('token', 'wechat_token');
      expect(wx.showToast).toHaveBeenCalledWith({
        title: '登录成功',
        icon: 'success'
      });
    });
    
    test('微信登录失败应该显示错误信息', async () => {
      userApi.wechatLogin.mockRejectedValue(new Error('网络错误'));
      
      await loginPage.loginWithWechat();
      await waitFor(50);
      
      expect(loginPage.data.loginTip).toBe('微信登录失败，请重试');
      expect(loginPage.data.loading).toBe(false);
    });
  });
});
