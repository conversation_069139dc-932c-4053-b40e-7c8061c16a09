const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');
const notificationService = require('../services/notificationService');
const {canAccessUserResource} = require('../middlewares/auth');

/**
 * 获取当前用户的通知列表
 * @route GET /api/notifications
 */
const getNotifications = async (req, res) => {
  try {
    const {page = 1, limit = 20, type, read} = req.query;
    const userId = req.user.id;

    // 构建查询条件
    const where = {userId};

    if (type) {
      where.type = type;
    }

    if (read !== undefined) {
      where.read = read === 'true';
    }

    // 分页查询
    const notifications = await prisma.notification.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (parseInt(page) - 1) * parseInt(limit),
      take: parseInt(limit)
    });

    // 获取总数
    const total = await prisma.notification.count({where});

    // 获取未读数量
    const unreadCount = await notificationService.getUnreadCount(userId);

    return success(res, {
      notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      unreadCount
    });
  } catch (err) {
    console.error('Get notifications error:', err);
    return error(res, 'Failed to get notifications', 500);
  }
};

/**
 * 获取指定通知
 * @route GET /api/notifications/:id
 */
const getNotificationById = async (req, res) => {
  try {
    const {id} = req.params;

    const notification = await prisma.notification.findUnique({
      where: {id},
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    if (!notification) {
      return error(res, 'Notification not found', 404);
    }

    return success(res, notification);
  } catch (err) {
    console.error('Get notification error:', err);
    return error(res, 'Failed to get notification', 500);
  }
};

/**
 * 创建通知
 * @route POST /api/notifications
 */
const createNotification = async (req, res) => {
  try {
    const {content} = req.body;
    const userId = req.user.id;

    if (!content) {
      return error(res, 'Content is required', 400);
    }

    // 创建通知
    const notification = await prisma.notification.create({
      data: {
        content,
        userId,
        read: false
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    return success(res, notification, 'Notification created successfully', 201);
  } catch (err) {
    console.error('Create notification error:', err);
    return error(res, 'Failed to create notification', 500);
  }
};

/**
 * 更新通知
 * @route PUT /api/notifications/:id
 */
const updateNotification = async (req, res) => {
  try {
    const {id} = req.params;
    const {content, read} = req.body;

    // 检查通知是否存在
    const existingNotification = await prisma.notification.findUnique({
      where: {id}
    });

    if (!existingNotification) {
      return error(res, 'Notification not found', 404);
    }

    // 检查权限（只有管理员或通知创建者可以更新内容）
    if (
      content &&
      req.user.role !== 'admin' &&
      req.user.id !== existingNotification.userId
    ) {
      return error(res, 'Permission denied', 403);
    }

    // 准备更新数据
    const updateData = {};

    if (content) updateData.content = content;
    if (read !== undefined) updateData.read = read;

    // 更新通知
    const updatedNotification = await prisma.notification.update({
      where: {id},
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    return success(
      res,
      updatedNotification,
      'Notification updated successfully'
    );
  } catch (err) {
    console.error('Update notification error:', err);
    return error(res, 'Failed to update notification', 500);
  }
};

/**
 * 删除通知
 * @route DELETE /api/notifications/:id
 */
const deleteNotification = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查通知是否存在
    const existingNotification = await prisma.notification.findUnique({
      where: {id}
    });

    if (!existingNotification) {
      return error(res, 'Notification not found', 404);
    }

    // 检查权限（只有管理员或通知创建者可以删除）
    if (
      req.user.role !== 'admin' &&
      req.user.id !== existingNotification.userId
    ) {
      return error(res, 'Permission denied', 403);
    }

    // 删除通知
    await prisma.notification.delete({
      where: {id}
    });

    return success(res, null, 'Notification deleted successfully');
  } catch (err) {
    console.error('Delete notification error:', err);
    return error(res, 'Failed to delete notification', 500);
  }
};

/**
 * 标记通知为已读
 * @route PUT /api/notifications/:id/read
 */
const markAsRead = async (req, res) => {
  try {
    const {id} = req.params;
    const userId = req.user.id;

    const result = await notificationService.markAsRead(id, userId);

    if (result.count === 0) {
      return error(res, 'Notification not found or access denied', 404);
    }

    return success(res, null, 'Notification marked as read');
  } catch (err) {
    console.error('Mark notification as read error:', err);
    return error(res, 'Failed to mark notification as read', 500);
  }
};

/**
 * 标记所有通知为已读
 * @route PUT /api/notifications/read-all
 */
const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await notificationService.markAllAsRead(userId);

    return success(
      res,
      {count: result.count},
      'All notifications marked as read'
    );
  } catch (err) {
    console.error('Mark all notifications as read error:', err);
    return error(res, 'Failed to mark all notifications as read', 500);
  }
};

/**
 * 获取未读通知数量
 * @route GET /api/notifications/unread-count
 */
const getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;

    const count = await notificationService.getUnreadCount(userId);

    return success(res, {count});
  } catch (err) {
    console.error('Get unread count error:', err);
    return error(res, 'Failed to get unread count', 500);
  }
};

/**
 * 发送家庭通知（仅家庭管理员和管理员）
 * @route POST /api/notifications/family
 */
const sendFamilyNotification = async (req, res) => {
  try {
    const {content} = req.body;
    const sender = req.user;

    if (!content) {
      return error(res, 'Content is required', 400);
    }

    // 检查权限
    if (!['admin', 'family_head'].includes(sender.role)) {
      return error(
        res,
        'Permission denied. Only family heads and admins can send family notifications',
        403
      );
    }

    const notifications = await notificationService.pushToFamily(
      content,
      'family_announcement',
      sender.id
    );

    return success(
      res,
      {count: notifications.length},
      'Family notification sent successfully',
      201
    );
  } catch (err) {
    console.error('Send family notification error:', err);
    return error(res, 'Failed to send family notification', 500);
  }
};

/**
 * 发送系统维护通知（仅管理员）
 * @route POST /api/notifications/system
 */
const sendSystemNotification = async (req, res) => {
  try {
    const {content} = req.body;
    const sender = req.user;

    if (!content) {
      return error(res, 'Content is required', 400);
    }

    // 检查权限
    if (sender.role !== 'admin') {
      return error(
        res,
        'Permission denied. Only admins can send system notifications',
        403
      );
    }

    const notifications = await notificationService.notifySystemMaintenance(
      content
    );

    return success(
      res,
      {count: notifications.length},
      'System notification sent successfully',
      201
    );
  } catch (err) {
    console.error('Send system notification error:', err);
    return error(res, 'Failed to send system notification', 500);
  }
};

module.exports = {
  getNotifications,
  getNotificationById,
  createNotification,
  updateNotification,
  deleteNotification,
  markAsRead,
  markAllAsRead,
  getUnreadCount,
  sendFamilyNotification,
  sendSystemNotification
};
