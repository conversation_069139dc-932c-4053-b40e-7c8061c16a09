<view class="container">
  <view class="mine-user-card">
    <view class="mine-user-avatar">
      <image wx:if="{{userInfo.avatar}}" src="{{userInfo.avatar}}" mode="aspectFill" />
      <van-icon wx:else name="user-o" size="64rpx" color="#fff" />
    </view>
    <view class="mine-user-info">
      <view class="mine-user-name">{{userInfo.name}}</view>
      <view class="mine-user-phone" wx:if="{{formattedPhone}}">手机号：{{formattedPhone}}</view>
    </view>
  </view>

  <view class="mine-links-section">
    <button class="mine-link-btn message" bindtap="goToFamilyMessage">
      <van-icon name="chat-o" size="36rpx" />
      <text style="margin-left: 16rpx;">家庭留言</text>
    </button>

    <button class="mine-link-btn notice" bindtap="goToNotice">
      <van-icon name="bell" size="36rpx" />
      <text style="margin-left: 16rpx;">通知中心</text>
    </button>
  </view>

  <button class="mine-action-btn" bindtap="onLogout" style="background:linear-gradient(90deg,#23272F,#181A20);color:#00F2EA;">
    <van-icon name="/assets/svg/layout.svg" size="36rpx" />
    <text style="margin-left: 16rpx;">退出登录</text>
  </button>
</view>