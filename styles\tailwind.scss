/* Tailwind CSS 风格的小程序样式 */

/* 基础颜色变量 */
:root {
  /* 主色调 - 蓝色系 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* 灰色系 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 成功色 */
  --green-500: #10b981;
  --green-600: #059669;

  /* 警告色 */
  --yellow-500: #f59e0b;
  --yellow-600: #d97706;

  /* 错误色 */
  --red-500: #ef4444;
  --red-600: #dc2626;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 基础样式重置 */
page {
  background-color: var(--gray-50);
  color: var(--gray-900);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.5;
}

/* 容器样式 */
.container {
  padding: 32rpx;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: var(--shadow);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.card-header {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
}

.btn-primary:active {
  background-color: var(--primary-700);
  transform: scale(0.98);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-700);
}

.btn-secondary:active {
  background-color: var(--gray-200);
}

.btn-success {
  background-color: var(--green-500);
  color: white;
}

.btn-success:active {
  background-color: var(--green-600);
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid var(--gray-200);
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: white;
  transition: border-color 0.2s;
}

.input:focus {
  border-color: var(--primary-500);
  outline: none;
}

.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  font-size: 28rpx;
  color: var(--gray-700);
  margin-bottom: 12rpx;
  font-weight: 500;
}

/* 文本样式 */
.text-xs {
  font-size: 24rpx;
}
.text-sm {
  font-size: 26rpx;
}
.text-base {
  font-size: 28rpx;
}
.text-lg {
  font-size: 32rpx;
}
.text-xl {
  font-size: 36rpx;
}
.text-2xl {
  font-size: 48rpx;
}

.text-gray-400 {
  color: var(--gray-400);
}
.text-gray-500 {
  color: var(--gray-500);
}
.text-gray-600 {
  color: var(--gray-600);
}
.text-gray-700 {
  color: var(--gray-700);
}
.text-gray-900 {
  color: var(--gray-900);
}

.text-primary {
  color: var(--primary-600);
}
.text-green {
  color: var(--green-500);
}
.text-red {
  color: var(--red-500);
}

.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}

/* 间距样式 */
.m-1 {
  margin: 8rpx;
}
.m-2 {
  margin: 16rpx;
}
.m-3 {
  margin: 24rpx;
}
.m-4 {
  margin: 32rpx;
}

.mt-1 {
  margin-top: 8rpx;
}
.mt-2 {
  margin-top: 16rpx;
}
.mt-3 {
  margin-top: 24rpx;
}
.mt-4 {
  margin-top: 32rpx;
}

.mb-1 {
  margin-bottom: 8rpx;
}
.mb-2 {
  margin-bottom: 16rpx;
}
.mb-3 {
  margin-bottom: 24rpx;
}
.mb-4 {
  margin-bottom: 32rpx;
}

.p-1 {
  padding: 8rpx;
}
.p-2 {
  padding: 16rpx;
}
.p-3 {
  padding: 24rpx;
}
.p-4 {
  padding: 32rpx;
}

/* 布局样式 */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}

.w-full {
  width: 100%;
}
.w-32 {
  width: 128rpx;
}
.h-full {
  height: 100%;
}
.h-32 {
  height: 128rpx;
}

.text-center {
  text-align: center;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.ml-1 {
  margin-left: 8rpx;
}
.ml-2 {
  margin-left: 16rpx;
}
.mr-3 {
  margin-right: 24rpx;
}
.mt-4 {
  margin-top: 32rpx;
}

.min-h-20 {
  min-height: 160rpx;
}
.min-h-32 {
  min-height: 256rpx;
}
.h-48 {
  height: 384rpx;
}

.opacity-50 {
  opacity: 0.5;
}

/* 圆角样式 */
.rounded {
  border-radius: 8rpx;
}
.rounded-md {
  border-radius: 12rpx;
}
.rounded-lg {
  border-radius: 16rpx;
}
.rounded-xl {
  border-radius: 24rpx;
}
.rounded-full {
  border-radius: 50%;
}

/* 阴影样式 */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}
.shadow {
  box-shadow: var(--shadow);
}
.shadow-md {
  box-shadow: var(--shadow-md);
}
.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* 背景样式 */
.bg-white {
  background-color: white;
}
.bg-gray-50 {
  background-color: var(--gray-50);
}
.bg-gray-100 {
  background-color: var(--gray-100);
}
.bg-primary {
  background-color: var(--primary-600);
}
.bg-green {
  background-color: var(--green-500);
}

/* 边框样式 */
.border {
  border: 2rpx solid var(--gray-200);
}
.border-gray-200 {
  border-color: var(--gray-200);
}
.border-primary {
  border-color: var(--primary-500);
}

/* 过渡动画 */
.transition {
  transition: all 0.2s;
}
.transition-colors {
  transition:
    color 0.2s,
    background-color 0.2s,
    border-color 0.2s;
}

/* 特殊组件样式 */
.tab-bar {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: var(--shadow);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: var(--gray-600);
  transition: all 0.2s;
}

.tab-item.active {
  background-color: var(--primary-600);
  color: white;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

.badge-green {
  background-color: #dcfce7;
  color: #166534;
}

.badge-red {
  background-color: #fee2e2;
  color: #991b1b;
}
