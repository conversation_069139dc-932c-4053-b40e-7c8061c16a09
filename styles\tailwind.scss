/* Tailwind CSS 风格的小程序样式 - 兼容版本 */

/*
 * 微信小程序不支持 CSS 变量，所以直接使用颜色值
 *
 * 颜色系统：
 * 主色调 - 蓝色系: #3b82f6 (primary-500), #2563eb (primary-600)
 * 粉色系: #ec4899 (pink-500), #db2777 (pink-600)
 * 灰色系: #f9fafb (gray-50), #111827 (gray-900)
 * 成功色: #10b981 (green-500)
 * 错误色: #ef4444 (red-500)
 */

/* 基础样式重置 */
page {
  background-color: #f9fafb;
  color: #111827;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.5;
}

/* 容器样式 */
.container {
  padding: 32rpx;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.card-header {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:active {
  background-color: #1d4ed8;
  transform: scale(0.98);
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-secondary:active {
  background-color: #e5e7eb;
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:active {
  background-color: #059669;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: white;
  transition: border-color 0.2s;
}

.input:focus {
  border-color: #3b82f6;
  outline: none;
}

.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 12rpx;
  font-weight: 500;
}

/* 文本样式 */
.text-xs {
  font-size: 24rpx;
}

.text-sm {
  font-size: 26rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 48rpx;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-900 {
  color: #111827;
}

.text-primary {
  color: #2563eb;
}

.text-green {
  color: #10b981;
}

.text-red {
  color: #ef4444;
}

.text-white {
  color: #fff
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* 间距样式 */
.m-1 {
  margin: 8rpx;
}

.m-2 {
  margin: 16rpx;
}

.m-3 {
  margin: 24rpx;
}

.m-4 {
  margin: 32rpx;
}

.mt-1 {
  margin-top: 8rpx;
}

.mt-2 {
  margin-top: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

.mb-1 {
  margin-bottom: 8rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.p-1 {
  padding: 8rpx;
}

.p-2 {
  padding: 16rpx;
}

.p-3 {
  padding: 24rpx;
}

.p-4 {
  padding: 32rpx;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.w-full {
  width: 100%;
}

.w-32 {
  width: 128rpx;
}

.h-full {
  height: 100%;
}

.h-32 {
  height: 128rpx;
}

.text-center {
  text-align: center;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.ml-1 {
  margin-left: 8rpx;
}

.ml-2 {
  margin-left: 16rpx;
}

.mr-3 {
  margin-right: 24rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

.min-h-20 {
  min-height: 160rpx;
}

.min-h-32 {
  min-height: 256rpx;
}

.h-48 {
  height: 384rpx;
}

.opacity-50 {
  opacity: 0.5;
}

/* 圆角样式 */
.rounded {
  border-radius: 8rpx;
}

.rounded-md {
  border-radius: 12rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-xl {
  border-radius: 24rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影样式 */
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow {
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-md {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 背景样式 */
.bg-white {
  background-color: white;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-primary {
  background-color: #2563eb;
}

.bg-green {
  background-color: #10b981;
}

/* 边框样式 */
.border {
  border: 2rpx solid #e5e7eb;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.border-primary {
  border-color: #3b82f6;
}

/* 过渡动画 */
.transition {
  transition: all 0.2s;
}

.transition-colors {
  transition:
    color 0.2s,
    background-color 0.2s,
    border-color 0.2s;
}

/* 特殊组件样式 */
.tab-bar {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #4b5563;
  transition: all 0.2s;
}

.tab-item.active {
  background-color: #2563eb;
  color: white;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.badge-primary {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.badge-green {
  background-color: #dcfce7;
  color: #166534;
}

.badge-red {
  background-color: #fee2e2;
  color: #991b1b;
}