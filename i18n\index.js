/**
 * 获取当前使用的语言
 */
function getLanguage() {
  const Language = wx.getStorageSync('Language')
  if (Language) {
    return Language
  }
  const allowLanguage = ['zh_CN', 'en'] // 目前支持的语言包
  const appBaseInfo = wx.getAppBaseInfo()
  let _language = appBaseInfo.language || 'zh_CN'
  if (!allowLanguage.includes(_language)) {
    _language = 'zh_CN'
  }
  wx.setStorageSync('Language', _language)
  return _language
}

function $t() {
  return require(getLanguage() + '.js');
}

function setTabBarLanguage() {
  const $t = this.$t()
  // wx.setTabBarItem({
  //   index: 0,
  //   pagePath: "pages/index/index",
  //   iconPath: "images/nav/index-off.png",
  //   selectedIconPath: "images/nav/index-on.png",
  //   text: $t.index.order
  // })
}
module.exports = {
  setTabBarLanguage: setTabBarLanguage,
  getLanguage: getLanguage,
  $t: $t,
  langs: [{
      name: '简体中文',
      code: 'zh_CN'
    },
    {
      name: 'English',
      code: 'en'
    }
  ]
}