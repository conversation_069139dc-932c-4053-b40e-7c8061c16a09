const prisma = require('../utils/prisma');
const {hashPassword} = require('../utils/password');
const {success, error} = require('../utils/response');

/**
 * 获取用户列表
 * @route GET /api/users
 */
const getUsers = async (req, res) => {
  try {
    const {
      page = 1,
      size = 10,
      search = '',
      role = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const pageNum = parseInt(page);
    const pageSize = parseInt(size);
    const skip = (pageNum - 1) * pageSize;

    // 构建查询条件
    const where = {};

    if (search) {
      where.OR = [
        {name: {contains: search, mode: 'insensitive'}},
        {phone: {contains: search, mode: 'insensitive'}}
      ];
    }

    if (role) {
      where.role = role;
    }

    // 获取总数
    const total = await prisma.user.count({where});

    // 获取用户列表
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        phone: true,
        avatar: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            orders: true,
            messages: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: pageSize
    });

    // 格式化返回数据
    const formattedUsers = users.map(user => ({
      ...user,
      orderCount: user._count.orders,
      messageCount: user._count.messages,
      status: 1 // 默认状态为激活，后续可以从数据库字段获取
    }));

    return success(res, {
      list: formattedUsers,
      total,
      page: pageNum,
      size: pageSize,
      totalPages: Math.ceil(total / pageSize)
    });
  } catch (err) {
    console.error('Get users error:', err);
    return error(res, 'Failed to get users', 500);
  }
};

/**
 * 获取指定用户
 * @route GET /api/users/:id
 */
const getUserById = async (req, res) => {
  try {
    const {id} = req.params;

    const user = await prisma.user.findUnique({
      where: {id},
      select: {
        id: true,
        name: true,
        phone: true,
        avatar: true,
        role: true,
        createdAt: true
      }
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    return success(res, user);
  } catch (err) {
    console.error('Get user error:', err);
    return error(res, 'Failed to get user', 500);
  }
};

/**
 * 创建用户
 * @route POST /api/users
 */
const createUser = async (req, res) => {
  try {
    const {name, phone, password, role} = req.body;

    if (!name || !phone || !password) {
      return error(res, 'Name, phone and password are required', 400);
    }

    // 检查手机号是否已存在
    const existingUser = await prisma.user.findUnique({
      where: {phone}
    });

    if (existingUser) {
      return error(res, 'Phone number already registered', 409);
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        phone,
        password: hashedPassword,
        role: role || 'user'
      }
    });

    return success(
      res,
      {
        id: user.id,
        name: user.name,
        phone: user.phone,
        role: user.role,
        createdAt: user.createdAt
      },
      'User created successfully',
      201
    );
  } catch (err) {
    console.error('Create user error:', err);
    return error(res, 'Failed to create user', 500);
  }
};

/**
 * 更新用户
 * @route PUT /api/users/:id
 */
const updateUser = async (req, res) => {
  try {
    const {id} = req.params;
    const {name, phone, password, avatar, role} = req.body;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: {id}
    });

    if (!existingUser) {
      return error(res, 'User not found', 404);
    }

    // 如果更新手机号，检查是否已被使用
    if (phone && phone !== existingUser.phone) {
      const phoneExists = await prisma.user.findUnique({
        where: {phone}
      });

      if (phoneExists) {
        return error(res, 'Phone number already in use', 409);
      }
    }

    // 准备更新数据
    const updateData = {};

    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (avatar) updateData.avatar = avatar;
    if (role) updateData.role = role;

    // 如果更新密码，需要哈希处理
    if (password) {
      updateData.password = await hashPassword(password);
    }

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: {id},
      data: updateData,
      select: {
        id: true,
        name: true,
        phone: true,
        avatar: true,
        role: true,
        updatedAt: true
      }
    });

    return success(res, updatedUser, 'User updated successfully');
  } catch (err) {
    console.error('Update user error:', err);
    return error(res, 'Failed to update user', 500);
  }
};

/**
 * 删除用户
 * @route DELETE /api/users/:id
 */
const deleteUser = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: {id}
    });

    if (!existingUser) {
      return error(res, 'User not found', 404);
    }

    // 删除用户
    await prisma.user.delete({
      where: {id}
    });

    return success(res, null, 'User deleted successfully');
  } catch (err) {
    console.error('Delete user error:', err);
    return error(res, 'Failed to delete user', 500);
  }
};

/**
 * 获取家庭成员列表
 * @route GET /api/users/family
 */
const getFamilyMembers = async (req, res) => {
  try {
    // 获取角色为 'family' 的用户
    const familyMembers = await prisma.user.findMany({
      where: {role: 'family'},
      select: {
        id: true,
        name: true,
        avatar: true,
        createdAt: true
      }
    });

    return success(res, familyMembers);
  } catch (err) {
    console.error('Get family members error:', err);
    return error(res, 'Failed to get family members', 500);
  }
};

/**
 * 获取用户统计信息
 * @route GET /api/users/statistics
 */
const getUserStatistics = async (req, res) => {
  try {
    // 获取用户总数
    const totalUsers = await prisma.user.count();

    // 按角色统计
    const roleStats = await prisma.user.groupBy({
      by: ['role'],
      _count: {
        id: true
      }
    });

    // 今日新增用户
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: today
        }
      }
    });

    // 本月新增用户
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);
    const monthUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: thisMonth
        }
      }
    });

    // 活跃用户（有订单的用户）
    const activeUsers = await prisma.user.count({
      where: {
        orders: {
          some: {}
        }
      }
    });

    const statistics = {
      total: totalUsers,
      today: todayUsers,
      month: monthUsers,
      active: activeUsers,
      roleDistribution: roleStats.reduce((acc, stat) => {
        acc[stat.role] = stat._count.id;
        return acc;
      }, {})
    };

    return success(res, statistics);
  } catch (err) {
    console.error('Get user statistics error:', err);
    return error(res, 'Failed to get user statistics', 500);
  }
};

/**
 * 重置用户密码
 * @route PUT /api/users/:id/password
 */
const resetUserPassword = async (req, res) => {
  try {
    const {id} = req.params;
    const {password} = req.body;

    if (!password || password.length < 6) {
      return error(res, 'Password must be at least 6 characters long', 400);
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: {id}
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    // 哈希新密码
    const hashedPassword = await hashPassword(password);

    // 更新密码
    await prisma.user.update({
      where: {id},
      data: {password: hashedPassword}
    });

    return success(res, null, 'Password reset successfully');
  } catch (err) {
    console.error('Reset password error:', err);
    return error(res, 'Failed to reset password', 500);
  }
};

/**
 * 批量删除用户
 * @route DELETE /api/users/batch
 */
const batchDeleteUsers = async (req, res) => {
  try {
    const {ids} = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return error(res, 'User IDs are required', 400);
    }

    // 检查是否包含管理员用户
    const adminUsers = await prisma.user.findMany({
      where: {
        id: {in: ids},
        role: 'admin'
      }
    });

    if (adminUsers.length > 0) {
      return error(res, 'Cannot delete admin users', 403);
    }

    // 批量删除用户
    const result = await prisma.user.deleteMany({
      where: {
        id: {in: ids}
      }
    });

    return success(
      res,
      {deletedCount: result.count},
      'Users deleted successfully'
    );
  } catch (err) {
    console.error('Batch delete users error:', err);
    return error(res, 'Failed to delete users', 500);
  }
};

module.exports = {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  getFamilyMembers,
  getUserStatistics,
  resetUserPassword,
  batchDeleteUsers
};
