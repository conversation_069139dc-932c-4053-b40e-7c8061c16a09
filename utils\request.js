/**
 * HTTP 请求工具类
 * 封装微信小程序的网络请求 API
 */

// 导入 mock API
const mockApi = require('../mock/api/index');

// 是否使用 mock 数据
const USE_MOCK = false;

// API 基础 URL
const BASE_URL = 'http://localhost:3000/api';

/**
 * 发送 HTTP 请求
 * @param {Object} options - 请求选项
 * @returns {Promise} - 返回 Promise 对象
 */
function request(options) {
  // 如果使用 mock 数据
  if (USE_MOCK) {
    return mockRequest(options);
  }

  // 实际的 HTTP 请求
  return new Promise((resolve, reject) => {
    const {url, method = 'GET', data = {}, header = {}} = options;

    // 添加认证信息
    const token = wx.getStorageSync('token');
    const headers = {
      'Content-Type': 'application/json',
      ...header
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    wx.request({
      url: `${BASE_URL}${url}`,
      method,
      data,
      header: headers,
      success: res => {
        // 请求成功
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          // 请求失败
          reject({
            code: res.statusCode,
            message: res.data.message || '请求失败',
            data: res.data
          });
        }
      },
      fail: err => {
        // 网络错误
        reject({
          code: -1,
          message: err.errMsg || '网络错误',
          data: null
        });
      }
    });
  });
}

/**
 * 发送模拟请求
 * @param {Object} options - 请求选项
 * @returns {Promise} - 返回 Promise 对象
 */
function mockRequest(options) {
  const {url, method = 'GET', data = {}} = options;

  // 调用 mock API
  return mockApi.request(url, method, data);
}

/**
 * GET 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求参数
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function get(url, data = {}, header = {}) {
  return request({
    url,
    method: 'GET',
    data,
    header
  });
}

/**
 * POST 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function post(url, data = {}, header = {}) {
  return request({
    url,
    method: 'POST',
    data,
    header
  });
}

/**
 * PUT 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function put(url, data = {}, header = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    header
  });
}

/**
 * DELETE 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function del(url, data = {}, header = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    header
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  del
};
