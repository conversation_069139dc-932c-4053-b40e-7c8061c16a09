// 模拟菜品类别
const mockCategories = [
  {id: 1, name: '川菜'},
  {id: 2, name: '粤菜'},
  {id: 3, name: '家常菜'},
  {id: 4, name: '素菜'}
];

// 模拟菜品数据
const mockDishes = [
  {
    id: 1,
    name: '红烧肉',
    category_id: 3,
    image: '/assets/image/dishes/hongshaorou.jpg',
    description: '家常红烧肉，香糯可口',
    count: 3,
    created_at: '2025-04-30'
  },
  {
    id: 2,
    name: '清炒时蔬',
    category_id: 4,
    image: '/assets/image/dishes/qingchaoshisu.jpg',
    description: '新鲜时令蔬菜',
    count: 2,
    created_at: '2025-04-30'
  },
  {
    id: 3,
    name: '鱼香肉丝',
    category_id: 1,
    image: '/assets/image/dishes/yuxiangrousi.jpg',
    description: '川式经典小炒',
    count: 0,
    created_at: '2025-04-30'
  },
  {
    id: 4,
    name: '糖醋里脊',
    category_id: 2,
    image: '/assets/image/dishes/tangculiji.jpg',
    description: '酸甜可口的粤式糖醋里脊',
    count: 0,
    created_at: '2025-04-30'
  }
];

// 模拟今日菜单
const mockTodayMenu = {
  date: '2025-04-30',
  dishes: [
    {
      id: 1,
      dish_id: 1,
      name: '红烧肉',
      count: 3,
      ordered_by: [2, 3, 4], // user_ids
      created_at: '2025-04-30 08:00:00'
    },
    {
      id: 2,
      dish_id: 2,
      name: '清炒时蔬',
      count: 2,
      ordered_by: [1, 5],
      created_at: '2025-04-30 08:00:00'
    }
  ]
};

// 模拟历史菜单
const mockHistoryMenus = [
  {
    date: '2025-04-29',
    dishes: [
      {
        id: 1,
        dish_id: 3,
        name: '鱼香肉丝',
        count: 2,
        ordered_by: [2, 4],
        created_at: '2025-04-29 08:00:00'
      },
      {
        id: 2,
        dish_id: 4,
        name: '糖醋里脊',
        count: 3,
        ordered_by: [1, 3, 5],
        created_at: '2025-04-29 08:00:00'
      }
    ]
  }
];

module.exports = {
  mockCategories,
  mockDishes,
  mockTodayMenu,
  mockHistoryMenus
};
