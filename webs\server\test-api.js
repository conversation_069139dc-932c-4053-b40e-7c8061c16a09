require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 测试用户登录信息
const testUser = {
  username: '13800138000',
  password: '123456'
};

let authToken = '';

async function testAPI() {
  console.log('🧪 开始API测试...\n');

  try {
    // 1. 测试用户登录
    console.log('1️⃣ 测试用户登录...');
    const loginRes = await axios.post(`${BASE_URL}/auth/login`, {
      username: testUser.username,
      password: testUser.password,
      loginType: 'password'
    });
    
    if (loginRes.data.code === 200) {
      authToken = loginRes.data.data.token;
      console.log('✅ 登录成功，获取token');
      console.log('   用户:', loginRes.data.data.user.name);
    } else {
      console.log('❌ 登录失败:', loginRes.data.message);
      return;
    }

    // 2. 测试获取今日菜单
    console.log('\n2️⃣ 测试获取今日菜单...');
    const todayMenuRes = await axios.get(`${BASE_URL}/menus/today`);
    console.log('✅ 今日菜单:', todayMenuRes.data.data ? '有数据' : '无数据');
    if (todayMenuRes.data.data) {
      console.log('   菜品数量:', todayMenuRes.data.data.dishes?.length || 0);
    }

    // 3. 测试获取菜品分类
    console.log('\n3️⃣ 测试获取菜品分类...');
    const categoriesRes = await axios.get(`${BASE_URL}/menus/categories`);
    console.log('✅ 分类数量:', categoriesRes.data.data?.length || 0);

    // 4. 测试获取分类菜品（小程序格式）
    console.log('\n4️⃣ 测试获取分类菜品...');
    const dishesRes = await axios.get(`${BASE_URL}/dishes/by-category`);
    console.log('✅ 分类菜品:', Object.keys(dishesRes.data.data || {}));

    // 5. 测试获取推荐菜单
    console.log('\n5️⃣ 测试获取推荐菜单...');
    const recommendedRes = await axios.get(`${BASE_URL}/menus/recommended`);
    console.log('✅ 推荐菜品数量:', recommendedRes.data.data?.length || 0);

    // 6. 测试获取统计信息
    console.log('\n6️⃣ 测试获取统计信息...');
    const statsRes = await axios.get(`${BASE_URL}/menus/statistics`);
    console.log('✅ 统计信息:', statsRes.data.data);

    // 7. 测试获取历史菜单
    console.log('\n7️⃣ 测试获取历史菜单...');
    const historyRes = await axios.get(`${BASE_URL}/menus/history`);
    console.log('✅ 历史菜单数量:', historyRes.data.data?.length || 0);

    // 8. 测试创建订单
    console.log('\n8️⃣ 测试创建订单...');
    const orderData = {
      items: [
        { dishId: 'test-dish-1', dishName: '红烧肉', count: 1 },
        { dishId: 'test-dish-2', dishName: '宫保鸡丁', count: 1 }
      ],
      remark: '少放盐',
      diningTime: new Date().toISOString()
    };
    
    try {
      const orderRes = await axios.post(`${BASE_URL}/orders`, orderData, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ 订单创建成功:', orderRes.data.data?.id);
    } catch (err) {
      console.log('⚠️ 订单创建需要有效的菜品ID');
    }

    // 9. 测试获取今日订单
    console.log('\n9️⃣ 测试获取今日订单...');
    const todayOrdersRes = await axios.get(`${BASE_URL}/orders/today`);
    console.log('✅ 今日订单数量:', todayOrdersRes.data.data?.length || 0);

    // 10. 测试获取消息
    console.log('\n🔟 测试获取消息...');
    const messagesRes = await axios.get(`${BASE_URL}/messages`);
    console.log('✅ 消息数量:', messagesRes.data.data?.length || 0);

    // 11. 测试获取通知
    console.log('\n1️⃣1️⃣ 测试获取通知...');
    const notificationsRes = await axios.get(`${BASE_URL}/notifications`);
    console.log('✅ 通知数量:', notificationsRes.data.data?.length || 0);

    // 12. 测试获取用户信息
    console.log('\n1️⃣2️⃣ 测试获取用户信息...');
    const userRes = await axios.get(`${BASE_URL}/users`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ 用户数量:', userRes.data.data?.length || 0);

    console.log('\n🎉 API测试完成！所有接口正常工作');

  } catch (error) {
    console.error('❌ API测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testAPI();
