/* 首页 - 小程序兼容设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 欢迎卡片 */
.home-welcome-card {
  @include modern-card;
  @include card-primary;
  @include flex;
  @include items-center;
  @include gap-3;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  @include card-enter(0.1s);
  @include hover-lift;

  .home-welcome-left {
    @include flex-1;
    min-width: 0;
  }

  .home-welcome-title {
    @include text-xl;
    @include font-bold;
    @include text-primary;
    margin-bottom: 8rpx;
    text-align: left;
  }

  .home-welcome-desc {
    @include text-sm;
    @include text-gray-600;
    text-align: left;
    line-height: 1.6;
  }

  .home-welcome-img {
    width: 80rpx;
    height: 80rpx;
    @include rounded-lg;
    object-fit: cover;
    @include shadow-md;
    flex-shrink: 0;
    border: 2rpx solid $primary-solid;
  }
}

/* 今日菜单卡片 */
.home-menu-card {
  @include modern-card;
  @include card-primary;
  @include shadow-lg;
  margin-bottom: 32rpx;
  padding: 32rpx;
  @include card-enter(0.2s);
  @include hover-lift;
  .home-menu-header {
    @include flex;
    @include justify-between;
    @include items-start;
    @include mb-3;

    .home-menu-title {
      @include text-primary;
      @include text-lg;
      @include font-semibold;
      @include flex;
      @include flex-col;

      .title-content {
        @include flex;
        @include items-center;
        @include gap-2;
        height: 40rpx;
        line-height: 40rpx;
      }

      .menu-date {
        @include text-xs;
        @include text-secondary;
        font-weight: normal;
        margin-top: 4rpx;

        &.recommended {
          @include text-error;
          font-style: italic;
        }
      }
    }

    .theme-link {
      @include modern-btn;
      @include btn-secondary;
      @include text-xs;
      @include font-medium;
      text-decoration: none;
      padding: 8rpx 16rpx;
      min-width: 80rpx;
      height: 36rpx;
      @include rounded-full;

      .van-icon {
        margin-left: 6rpx;
        @include transition-transform;
        font-size: 20rpx;
      }

      &:active {
        @include btn-primary;
        transform: scale(0.95);

        .van-icon {
          transform: translateX(2rpx);
        }
      }
    }
  }
  .home-menu-list {
    @include flex;
    @include gap-3;
    @include overflow-auto;
    white-space: nowrap;
    padding-bottom: 12rpx;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .home-menu-food-card {
      min-width: 180rpx;
      max-width: 200rpx;
      @include modern-card;
      @include shadow-md;
      background: linear-gradient(135deg, $white 0%, $gray-50 100%);
      padding: 24rpx 16rpx;
      height: 180rpx;
      @include flex;
      @include flex-col;
      @include items-center;
      @include relative;
      @include transition;
      justify-content: flex-start;
      @include button-press;
      @include hover-lift;

      &:active {
        @include shadow-lg;
        transform: translateY(-4rpx) scale(1.05);
      }

      .home-menu-food-img {
        width: 64rpx;
        height: 64rpx;
        @include rounded-full;
        object-fit: cover;
        border: 3rpx solid $primary-solid;
        margin-bottom: 12rpx;
        @include shadow-sm;
      }

      .home-menu-food-name {
        @include text-gray-900;
        @include font-semibold;
        @include text-sm;
        @include text-center;
        margin-bottom: 4rpx;
        width: 100%;
        @include overflow-hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .home-menu-food-count {
        @include modern-badge;
        @include badge-primary;
        @include text-xs;
        @include absolute;
        top: 12rpx;
        right: 12rpx;
        min-width: 36rpx;
        height: 36rpx;
        line-height: 36rpx;
      }
    }
  }
}

// 家庭留言卡片
.home-message-card {
  @include modern-card;
  @include card-primary;
  @include shadow-lg;
  margin-bottom: 0;
  padding: 32rpx 24rpx;
  @include card-enter(0.3s);
  @include hover-lift;
  .home-message-header {
    @include flex;
    @include justify-between;
    @include items-center;
    @include mb-2;

    .theme-link {
      @include modern-btn;
      @include btn-secondary;
      @include text-xs;
      @include font-medium;
      text-decoration: none;
      padding: 8rpx 16rpx;
      min-width: 80rpx;
      height: 36rpx;
      @include rounded-full;

      .van-icon {
        margin-left: 6rpx;
        @include transition-transform;
        font-size: 20rpx;
      }

      &:active {
        @include btn-primary;
        transform: scale(0.95);

        .van-icon {
          transform: translateX(2rpx);
        }
      }
    }
  }

  .home-message-title {
    @include text-primary;
    @include text-lg;
    @include font-semibold;
    @include flex;
    @include items-center;
    @include gap-2;
  }
  .home-message-swiper {
    height: 64rpx;
    margin-top: 12rpx;
    @include modern-card;
    @include card-flat;
    @include rounded-lg;
    padding: 0 16rpx;
    @include flex;
    @include items-center;

    .home-message-swipe {
      height: 64rpx;
      width: 100%;
      @include flex;
      @include items-center;
    }

    swiper-item {
      height: 64rpx;
      @include flex;
      @include items-center;
      @include overflow-hidden;
    }

    .home-message-text {
      @include text-gray-700;
      @include text-sm;
      line-height: 1.4;
      white-space: nowrap;
      @include overflow-hidden;
      text-overflow: ellipsis;
      @include flex;
      @include items-center;
      height: 100%;
    }
  }
}

.notice-bar-wrap {
  width: 100%;
  margin-bottom: 12rpx;
  box-sizing: border-box;
  width: calc(100% - 48rpx);
  height: 78rpx;
}
.van-notice-bar {
  background: linear-gradient(90deg, #232323 60%, #181a20 100%) !important;
  border-radius: 32rpx !important;
  padding: 24rpx 24rpx !important;
  color: #00f2ea !important;
  font-size: 28rpx !important;
  font-weight: 600;
  box-shadow: 0 8rpx 36rpx #00f2ea22;
  border: 1rpx solid #00f2ea;
  display: flex;
  align-items: center;
  height: 78rpx !important;
  margin: 0 !important;
  .van-icon {
    color: rgba(236, 72, 153, 1) !important;
    font-size: 36rpx !important;
    margin-right: 16rpx;
  }
}
