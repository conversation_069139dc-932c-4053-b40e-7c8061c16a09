/* 首页 - 小程序兼容设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 欢迎卡片 */
.home-welcome-card {
  @include modern-card;
  @include card-primary;
  @include flex;
  @include items-center;
  @include gap-3;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;

  .home-welcome-left {
    @include flex-1;
    min-width: 0;
  }

  .home-welcome-title {
    @include text-xl;
    @include font-bold;
    @include text-primary;
    margin-bottom: 8rpx;
    text-align: left;
  }

  .home-welcome-desc {
    @include text-sm;
    @include text-gray-600;
    text-align: left;
    line-height: 1.6;
  }

  .home-welcome-img {
    width: 80rpx;
    height: 80rpx;
    @include rounded-lg;
    object-fit: cover;
    @include shadow-md;
    flex-shrink: 0;
    border: 2rpx solid $primary-solid;
  }
}

/* 今日菜单卡片 */
.home-menu-card {
  @include modern-card;
  @include card-primary;
  @include shadow-lg;
  margin-bottom: 32rpx;
  padding: 32rpx;
  .home-menu-header {
    @include flex;
    @include justify-between;
    @include items-start;
    @include mb-3;

    .home-menu-title {
      @include text-primary;
      @include text-lg;
      @include font-semibold;
      @include flex;
      @include flex-col;

      .title-content {
        @include flex;
        @include items-center;
        @include gap-2;
        height: 40rpx;
        line-height: 40rpx;
      }

      .menu-date {
        @include text-xs;
        @include text-secondary;
        font-weight: normal;
        margin-top: 4rpx;

        &.recommended {
          @include text-error;
          font-style: italic;
        }
      }
    }

    .theme-link {
      @include text-gray-600;
      @include text-sm;
      @include font-medium;
      @include transition;
      text-decoration: none;
      padding: 0 12rpx;
      @include flex;
      @include items-center;
      @include justify-end;
      line-height: 40rpx;
      height: 40rpx;

      .van-icon {
        margin-left: 4rpx;
        @include transition-transform;
      }

      &:active {
        @include text-primary;

        .van-icon {
          transform: translateX(4rpx);
        }
      }
    }
  }
  .home-menu-list {
    @include flex;
    @include gap-3;
    @include overflow-auto;
    white-space: nowrap;
    padding-bottom: 12rpx;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .home-menu-food-card {
      min-width: 180rpx;
      max-width: 200rpx;
      @include modern-card;
      @include shadow-md;
      background: linear-gradient(135deg, $white 0%, $gray-50 100%);
      padding: 24rpx 16rpx;
      height: 180rpx;
      @include flex;
      @include flex-col;
      @include items-center;
      @include relative;
      @include transition;
      justify-content: flex-start;

      &:active {
        @include shadow-lg;
        transform: translateY(-2rpx) scale(1.02);
      }

      .home-menu-food-img {
        width: 64rpx;
        height: 64rpx;
        @include rounded-full;
        object-fit: cover;
        border: 3rpx solid $primary-solid;
        margin-bottom: 12rpx;
        @include shadow-sm;
      }

      .home-menu-food-name {
        @include text-gray-900;
        @include font-semibold;
        @include text-sm;
        @include text-center;
        margin-bottom: 4rpx;
        width: 100%;
        @include overflow-hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .home-menu-food-count {
        @include modern-badge;
        @include badge-primary;
        @include text-xs;
        @include absolute;
        top: 12rpx;
        right: 12rpx;
        min-width: 36rpx;
        height: 36rpx;
        line-height: 36rpx;
      }
    }
  }
}

// 家庭留言卡片
.home-message-card {
  @include modern-card;
  @include card-primary;
  @include shadow-lg;
  margin-bottom: 0;
  padding: 32rpx 24rpx;
  .home-message-header {
    @include flex;
    @include justify-between;
    @include items-center;
    @include mb-2;

    .theme-link {
      @include text-gray-600;
      @include text-sm;
      @include font-medium;
      @include transition;
      text-decoration: none;
      padding: 0 12rpx;
      min-width: 80rpx;
      @include flex;
      @include items-center;
      @include justify-end;
      line-height: 40rpx;
      height: 40rpx;

      .van-icon {
        margin-left: 4rpx;
        @include transition-transform;
        font-size: 20rpx;
      }

      &:active {
        @include text-primary;

        .van-icon {
          transform: translateX(4rpx);
        }
      }
    }
  }

  .home-message-title {
    @include text-primary;
    @include text-lg;
    @include font-semibold;
    @include flex;
    @include items-center;
    @include gap-2;
  }
  .home-message-swiper {
    height: 80rpx;
    margin-top: 8rpx;

    .home-message-swipe {
      height: 80rpx;
      width: 100%;
    }

    swiper-item {
      height: 80rpx;
      line-height: 80rpx;
      @include overflow-hidden;
    }

    .home-message-text {
      @include text-gray-700;
      @include text-sm;
      @include text-center;
      line-height: 80rpx;
      white-space: nowrap;
      @include overflow-hidden;
      text-overflow: ellipsis;
    }
  }
}

.notice-bar-wrap {
  width: 100%;
  margin-bottom: 12rpx;
  box-sizing: border-box;
  width: calc(100% - 48rpx);
  height: 78rpx;
}
.van-notice-bar {
  background: linear-gradient(90deg, #232323 60%, #181a20 100%) !important;
  border-radius: 32rpx !important;
  padding: 24rpx 24rpx !important;
  color: #00f2ea !important;
  font-size: 28rpx !important;
  font-weight: 600;
  box-shadow: 0 8rpx 36rpx #00f2ea22;
  border: 1rpx solid #00f2ea;
  display: flex;
  align-items: center;
  height: 78rpx !important;
  margin: 0 !important;
  .van-icon {
    color: rgba(236, 72, 153, 1) !important;
    font-size: 36rpx !important;
    margin-right: 16rpx;
  }
}
