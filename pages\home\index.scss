/* 首页 - 现代化设计 */

.container {
  @extend .page-container;
  @extend .page-container-safe;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 欢迎卡片 */
.home-welcome-card {
  @extend .modern-card;
  @extend .card-primary;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;

  .home-welcome-left {
    flex: 1;
    min-width: 0;
  }

  .home-welcome-title {
    @extend .text-xl;
    @extend .font-bold;
    @extend .text-primary;
    margin-bottom: 8rpx;
    text-align: left;
  }

  .home-welcome-desc {
    @extend .text-sm;
    @extend .text-gray-600;
    text-align: left;
    line-height: 1.6;
  }

  .home-welcome-img {
    width: 80rpx;
    height: 80rpx;
    @extend .rounded-lg;
    object-fit: cover;
    @extend .shadow-md;
    flex-shrink: 0;
    border: 2rpx solid #6366f1;
  }
}

/* 今日菜单卡片 */
.home-menu-card {
  @extend .modern-card;
  @extend .card-primary;
  @extend .shadow-lg;
  margin-bottom: 32rpx;
  padding: 32rpx;
  .home-menu-header {
    @extend .flex;
    @extend .justify-between;
    @extend .items-start;
    @extend .mb-3;

    .home-menu-title {
      @extend .text-primary;
      @extend .text-lg;
      @extend .font-semibold;
      @extend .flex;
      @extend .flex-col;

      .title-content {
        @extend .flex;
        @extend .items-center;
        @extend .gap-2;
        height: 40rpx;
        line-height: 40rpx;
      }

      .menu-date {
        @extend .text-xs;
        @extend .text-secondary;
        font-weight: normal;
        margin-top: 4rpx;

        &.recommended {
          @extend .text-error;
          font-style: italic;
        }
      }
    }

    .theme-link {
      @extend .text-gray-600;
      @extend .text-sm;
      @extend .font-medium;
      @extend .transition;
      text-decoration: none;
      padding: 0 12rpx;
      @extend .flex;
      @extend .items-center;
      @extend .justify-end;
      line-height: 40rpx;
      height: 40rpx;

      .van-icon {
        margin-left: 4rpx;
        @extend .transition-transform;
      }

      &:active {
        @extend .text-primary;

        .van-icon {
          transform: translateX(4rpx);
        }
      }
    }
  }
  .home-menu-list {
    @extend .flex;
    @extend .gap-3;
    @extend .overflow-auto;
    white-space: nowrap;
    padding-bottom: 12rpx;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .home-menu-food-card {
      min-width: 180rpx;
      max-width: 200rpx;
      @extend .modern-card;
      @extend .shadow-md;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      padding: 24rpx 16rpx;
      height: 180rpx;
      @extend .flex;
      @extend .flex-col;
      @extend .items-center;
      @extend .relative;
      @extend .transition;
      justify-content: flex-start;

      &:active {
        @extend .shadow-lg;
        transform: translateY(-2rpx) scale(1.02);
      }

      .home-menu-food-img {
        width: 64rpx;
        height: 64rpx;
        @extend .rounded-full;
        object-fit: cover;
        border: 3rpx solid #6366f1;
        margin-bottom: 12rpx;
        @extend .shadow-sm;
      }

      .home-menu-food-name {
        @extend .text-gray-900;
        @extend .font-semibold;
        @extend .text-sm;
        @extend .text-center;
        margin-bottom: 4rpx;
        width: 100%;
        @extend .overflow-hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .home-menu-food-count {
        @extend .modern-badge;
        @extend .badge-primary;
        @extend .text-xs;
        @extend .absolute;
        top: 12rpx;
        right: 12rpx;
        min-width: 36rpx;
        height: 36rpx;
        line-height: 36rpx;
      }
    }
  }
}

// 家庭留言卡片
.home-message-card {
  @extend .modern-card;
  @extend .card-primary;
  @extend .shadow-lg;
  margin-bottom: 0;
  padding: 32rpx 24rpx;
  .home-message-header {
    @extend .flex;
    @extend .justify-between;
    @extend .items-center;
    @extend .mb-2;

    .theme-link {
      @extend .text-gray-600;
      @extend .text-sm;
      @extend .font-medium;
      @extend .transition;
      text-decoration: none;
      padding: 0 12rpx;
      min-width: 80rpx;
      @extend .flex;
      @extend .items-center;
      @extend .justify-end;
      line-height: 40rpx;
      height: 40rpx;

      .van-icon {
        margin-left: 4rpx;
        @extend .transition-transform;
        font-size: 20rpx;
      }

      &:active {
        @extend .text-primary;

        .van-icon {
          transform: translateX(4rpx);
        }
      }
    }
  }

  .home-message-title {
    @extend .text-primary;
    @extend .text-lg;
    @extend .font-semibold;
    @extend .flex;
    @extend .items-center;
    @extend .gap-2;
  }
  .home-message-swiper {
    height: 80rpx;
    margin-top: 8rpx;

    .home-message-swipe {
      height: 80rpx;
      width: 100%;
    }

    swiper-item {
      height: 80rpx;
      line-height: 80rpx;
      @extend .overflow-hidden;
    }

    .home-message-text {
      @extend .text-gray-700;
      @extend .text-sm;
      @extend .text-center;
      line-height: 80rpx;
      white-space: nowrap;
      @extend .overflow-hidden;
      text-overflow: ellipsis;
    }
  }
}

.notice-bar-wrap {
  width: 100%;
  margin-bottom: 12rpx;
  box-sizing: border-box;
  width: calc(100% - 48rpx);
  height: 78rpx;
}
.van-notice-bar {
  background: linear-gradient(90deg, #232323 60%, #181a20 100%) !important;
  border-radius: 32rpx !important;
  padding: 24rpx 24rpx !important;
  color: #00f2ea !important;
  font-size: 28rpx !important;
  font-weight: 600;
  box-shadow: 0 8rpx 36rpx #00f2ea22;
  border: 1rpx solid #00f2ea;
  display: flex;
  align-items: center;
  height: 78rpx !important;
  margin: 0 !important;
  .van-icon {
    color: rgba(236, 72, 153, 1) !important;
    font-size: 36rpx !important;
    margin-right: 16rpx;
  }
}
