<template>
  <div class="order-list">
    <CustomTable
      title="订单管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #userName="{ row }">
        <div class="user-info">
          <span>{{ row.user?.name || "未知用户" }}</span>
          <small>{{ row.user?.phone }}</small>
        </div>
      </template>

      <template #items="{ row }">
        <el-tooltip effect="dark" placement="top">
          <template #content>
            <div v-for="item in getOrderItems(row.items)" :key="item.dishName">
              {{ item.dishName }} x{{ item.count }}
            </div>
          </template>
          <span>{{ getOrderItems(row.items).length }}道菜</span>
        </el-tooltip>
      </template>

      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <template #mealTime="{ row }">
        {{ formatDateTime(row.mealTime) }}
      </template>

      <template #createdAt="{ row }">
        {{ formatDateTime(row.createdAt) }}
      </template>

      <template #actions="{ row }">
        <el-button size="small" @click="handleView(row)">查看</el-button>
        <el-dropdown @command="command => handleStatusChange(row, command)">
          <el-button size="small" type="primary">
            更新状态<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="pending">待处理</el-dropdown-item>
              <el-dropdown-item command="completed">已完成</el-dropdown-item>
              <el-dropdown-item command="cancelled">已取消</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button size="small" type="danger" @click="handleDelete(row)"
          >删除</el-button
        >
      </template>
    </CustomTable>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="detailVisible" title="订单详情" width="600px">
      <OrderDetail
        v-if="detailVisible"
        :order="selectedOrder"
        @close="detailVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  ArrowDown,
  View,
  Edit,
  Delete,
  Refresh
} from "@element-plus/icons-vue";
import CustomTable from "@/components/CustomTable.vue";
import OrderDetail from "./components/OrderDetail.vue";
import { orderApi } from "@/api/order";
import { formatTime } from "@/utils/common";
const loading = ref(false);
const tableData = ref([]);
const detailVisible = ref(false);
const selectedOrder = ref({});

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
});

const searchParams = reactive({
  status: "",
  userName: "",
  startDate: "",
  endDate: ""
});

// 表格列配置
const columns = [
  { prop: "id", label: "订单号", width: 100 },
  { prop: "userName", label: "用户信息", slot: "userName", minWidth: 120 },
  { prop: "items", label: "菜品", slot: "items", width: 100 },
  { prop: "status", label: "状态", slot: "status", width: 100 },
  { prop: "mealTime", label: "用餐时间", slot: "mealTime", width: 150 },
  { prop: "remark", label: "备注", showOverflowTooltip: true, minWidth: 120 },
  { prop: "createdAt", label: "下单时间", slot: "createdAt", width: 150 }
];

// 搜索字段配置
const searchFields = [
  {
    prop: "status",
    label: "状态",
    type: "select",
    placeholder: "选择状态",
    options: [
      { label: "待处理", value: "pending" },
      { label: "已完成", value: "completed" },
      { label: "已取消", value: "cancelled" }
    ]
  },
  {
    prop: "userName",
    label: "用户名",
    type: "input",
    placeholder: "请输入用户名"
  },
  {
    prop: "startDate",
    label: "开始日期",
    type: "date",
    placeholder: "选择开始日期"
  },
  {
    prop: "endDate",
    label: "结束日期",
    type: "date",
    placeholder: "选择结束日期"
  }
];

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    };

    const response = await orderApi.getOrders(params);
    if (response.data) {
      tableData.value = response.data.list || response.data;
      pagination.total = response.data.total || response.data.length;
    }
  } catch (error) {
    console.error("加载订单列表失败:", error);
    ElMessage.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = params => {
  Object.assign(searchParams, params);
  pagination.page = 1;
  loadData();
};

// 重置
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = "";
  });
  pagination.page = 1;
  loadData();
};

// 分页变化
const handleCurrentChange = page => {
  pagination.page = page;
  loadData();
};

const handleSizeChange = size => {
  pagination.size = size;
  pagination.page = 1;
  loadData();
};

// 查看详情
const handleView = row => {
  selectedOrder.value = row;
  detailVisible.value = true;
};

// 更新状态
const handleStatusChange = async (row, status) => {
  try {
    await orderApi.updateOrderStatus(row.id, status);
    ElMessage.success("状态更新成功");
    loadData();
  } catch (error) {
    console.error("更新订单状态失败:", error);
    ElMessage.error("状态更新失败");
  }
};

// 删除订单
const handleDelete = async row => {
  try {
    await ElMessageBox.confirm("确定要删除这个订单吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    await orderApi.deleteOrder(row.id);
    ElMessage.success("删除成功");
    loadData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除订单失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 获取订单项目
const getOrderItems = items => {
  try {
    return JSON.parse(items || "[]");
  } catch {
    return [];
  }
};

// 获取状态类型
const getStatusType = status => {
  const statusMap = {
    pending: "warning",
    completed: "success",
    cancelled: "danger"
  };
  return statusMap[status] || "info";
};

// 获取状态文本
const getStatusText = status => {
  const statusMap = {
    pending: "待处理",
    completed: "已完成",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知";
};

// 格式化日期时间
const formatDateTime = datetime => {
  return formatTime(datetime, "YYYY-MM-DD HH:mm");
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.order-list {
  padding: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  small {
    color: #999;
    font-size: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .order-list {
    padding: 16px;
  }

  .user-info {
    font-size: 14px;

    small {
      font-size: 11px;
    }
  }
}
</style>
