<script setup lang="ts">
import { ReNormalCountTo, ReboundCountTo } from "@/components/ReCountTo";

defineOptions({
  name: "CountTo"
});
</script>

<template>
  <el-card shadow="never">
    <template #header>
      <div class="card-header">
        <span class="font-medium">数字动画组件</span>
      </div>
    </template>
    <div class="flex items-center">
      <ReNormalCountTo
        prefix="$"
        :duration="4000"
        :color="'#409EFF'"
        :fontSize="'2em'"
        :startVal="5"
        :endVal="2000"
      />
      <ul class="flex ml-8">
        <ReboundCountTo
          v-for="(num, inx) of [1, 6, 6, 7, 8]"
          :key="inx"
          :i="num"
          :blur="inx"
          :delay="inx + 1"
        />
      </ul>
    </div>
  </el-card>
</template>
