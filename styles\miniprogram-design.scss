/* 小程序兼容的现代化设计系统 */
@import "./animations.scss";

// 🎨 设计令牌 (Design Tokens)

// 主色调
$primary-start: #4f46e5;
$primary-end: #7c3aed;
$primary-solid: #6366f1;

// 辅助色系
$secondary: #f59e0b;
$accent: #ec4899;
$success: #10b981;
$warning: #f59e0b;
$error: #ef4444;
$info: #3b82f6;

// 中性色系
$white: #ffffff;
$gray-50: #f8fafc;
$gray-100: #f1f5f9;
$gray-200: #e2e8f0;
$gray-300: #cbd5e1;
$gray-400: #94a3b8;
$gray-500: #64748b;
$gray-600: #475569;
$gray-700: #334155;
$gray-800: #1e293b;
$gray-900: #0f172a;

// 间距系统
$space-1: 8rpx;
$space-2: 16rpx;
$space-3: 24rpx;
$space-4: 32rpx;
$space-5: 40rpx;
$space-6: 48rpx;
$space-8: 64rpx;

// 圆角系统
$radius-xs: 8rpx;
$radius-sm: 12rpx;
$radius-md: 16rpx;
$radius-lg: 20rpx;
$radius-xl: 24rpx;
$radius-2xl: 32rpx;

// 阴影系统
$shadow-xs: 0 2rpx 8rpx rgba(15, 23, 42, 0.04);
$shadow-sm: 0 2rpx 16rpx rgba(15, 23, 42, 0.06);
$shadow-md: 0 8rpx 32rpx rgba(15, 23, 42, 0.08);
$shadow-lg: 0 16rpx 48rpx rgba(15, 23, 42, 0.12);
$shadow-xl: 0 24rpx 64rpx rgba(15, 23, 42, 0.16);

// 渐变系统
$gradient-primary: linear-gradient(135deg, $primary-start 0%, $primary-end 100%);
$gradient-secondary: linear-gradient(135deg, $secondary 0%, #fb923c 100%);
$gradient-success: linear-gradient(135deg, $success 0%, #059669 100%);
$gradient-bg: linear-gradient(180deg, $gray-50 0%, $gray-100 100%);

// 字体系统
$font-xs: 20rpx;
$font-sm: 24rpx;
$font-base: 28rpx;
$font-lg: 32rpx;
$font-xl: 36rpx;
$font-2xl: 48rpx;

// 🎯 Mixins (小程序兼容)

// 页面容器
@mixin page-container {
  min-height: 100vh;
  background: $gradient-bg;
  padding: $space-4;
}

@mixin page-container-safe {
  padding-bottom: calc(env(safe-area-inset-bottom) + 32rpx);
}

// 现代化卡片
@mixin modern-card {
  background: $white;
  border-radius: $radius-xl;
  box-shadow: $shadow-md;
  border: 2rpx solid $gray-100;
  padding: $space-4;
  margin-bottom: $space-3;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin card-primary {
  border-color: rgba($primary-solid, 0.2);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: $gradient-primary;
  }
}

@mixin card-elevated {
  box-shadow: $shadow-xl;
  border: none;
}

@mixin card-flat {
  box-shadow: none;
  border: 2rpx solid $gray-200;
}

// 现代化按钮
@mixin modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $space-3 $space-4;
  border-radius: $radius-sm;
  font-size: $font-base;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  box-sizing: border-box;
  min-height: 88rpx;
  position: relative;
  overflow: hidden;
}

@mixin btn-primary {
  background: $gradient-primary;
  color: $white;
  box-shadow: $shadow-sm;
}

@mixin btn-secondary {
  background: $white;
  color: $primary-solid;
  border: 2rpx solid $primary-solid;
}

@mixin btn-success {
  background: $gradient-success;
  color: $white;
  box-shadow: $shadow-sm;
}

@mixin btn-warning {
  background: $gradient-secondary;
  color: $white;
  box-shadow: $shadow-sm;
}

@mixin btn-danger {
  background: linear-gradient(135deg, $error 0%, #dc2626 100%);
  color: $white;
  box-shadow: $shadow-sm;
}

@mixin btn-ghost {
  background: transparent;
  color: $gray-600;
  border: 2rpx solid $gray-300;
}

@mixin btn-gradient {
  background: $gradient-primary;
  color: $white;
  box-shadow: $shadow-sm;
}

@mixin btn-sm {
  padding: $space-2 $space-3;
  font-size: $font-sm;
  min-height: 64rpx;
}

@mixin btn-lg {
  padding: $space-4 $space-6;
  font-size: $font-lg;
  min-height: 96rpx;
}

@mixin btn-full {
  width: 100%;
}

// 现代化输入框
@mixin modern-input {
  width: 100%;
  padding: $space-3 $space-4;
  border: 2rpx solid $gray-200;
  border-radius: $radius-sm;
  font-size: $font-base;
  background: $white;
  color: $gray-900;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  min-height: 88rpx;

  &:focus {
    border-color: $primary-solid;
    outline: none;
    box-shadow: 0 0 0 6rpx rgba($primary-solid, 0.1);
  }
}

// 布局 Mixins
@mixin flex {
  display: flex;
}

@mixin flex-col {
  flex-direction: column;
}

@mixin items-center {
  align-items: center;
}

@mixin items-start {
  align-items: flex-start;
}

@mixin items-end {
  align-items: flex-end;
}

@mixin justify-center {
  justify-content: center;
}

@mixin justify-between {
  justify-content: space-between;
}

@mixin justify-start {
  justify-content: flex-start;
}

@mixin justify-end {
  justify-content: flex-end;
}

@mixin gap-2 {
  gap: $space-2;
}

@mixin gap-3 {
  gap: $space-3;
}

@mixin gap-4 {
  gap: $space-4;
}

// 间距 Mixins
@mixin p-2 {
  padding: $space-2;
}

@mixin p-3 {
  padding: $space-3;
}

@mixin p-4 {
  padding: $space-4;
}

@mixin mb-1 {
  margin-bottom: $space-1;
}

@mixin mb-2 {
  margin-bottom: $space-2;
}

@mixin mb-3 {
  margin-bottom: $space-3;
}

@mixin mb-4 {
  margin-bottom: $space-4;
}

// 文字 Mixins
@mixin text-2xl {
  font-size: $font-2xl;
}

@mixin text-xs {
  font-size: $font-xs;
}

@mixin text-sm {
  font-size: $font-sm;
}

@mixin text-base {
  font-size: $font-base;
}

@mixin text-lg {
  font-size: $font-lg;
}

@mixin text-xl {
  font-size: $font-xl;
}

@mixin font-medium {
  font-weight: 500;
}

@mixin font-semibold {
  font-weight: 600;
}

@mixin font-bold {
  font-weight: 700;
}

@mixin text-center {
  text-align: center;
}

// 颜色 Mixins
@mixin text-white {
  color: $white;
}

@mixin text-gray-400 {
  color: $gray-400;
}

@mixin text-gray-500 {
  color: $gray-500;
}

@mixin text-gray-600 {
  color: $gray-600;
}

@mixin text-gray-700 {
  color: $gray-700;
}

@mixin text-gray-900 {
  color: $gray-900;
}

@mixin text-primary {
  color: $primary-solid;
}

@mixin text-secondary {
  color: $secondary;
}

@mixin text-warning {
  color: $warning;
}

@mixin text-success {
  color: $success;
}

@mixin text-error {
  color: $error;
}

// 背景 Mixins
@mixin bg-white {
  background-color: $white;
}

@mixin bg-gray-50 {
  background-color: $gray-50;
}

@mixin bg-gray-100 {
  background-color: $gray-100;
}

@mixin bg-primary {
  background-color: $primary-solid;
}

// 圆角 Mixins
@mixin rounded-sm {
  border-radius: $radius-sm;
}

@mixin rounded-md {
  border-radius: $radius-md;
}

@mixin rounded-lg {
  border-radius: $radius-lg;
}

@mixin rounded-xl {
  border-radius: $radius-xl;
}

@mixin rounded-full {
  border-radius: 9999rpx;
}

// 阴影 Mixins
@mixin shadow-xl {
  box-shadow: $shadow-xl;
}

@mixin shadow-sm {
  box-shadow: $shadow-sm;
}

@mixin shadow-md {
  box-shadow: $shadow-md;
}

@mixin shadow-lg {
  box-shadow: $shadow-lg;
}

// 其他工具 Mixins
@mixin overflow-hidden {
  overflow: hidden;
}

@mixin overflow-auto {
  overflow: auto;
}

@mixin flex-1 {
  flex: 1;
}

@mixin transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin transition-transform {
  transition: transform 0.2s ease;
}

// 位置 Mixins
@mixin relative {
  position: relative;
}

@mixin absolute {
  position: absolute;
}

// 徽章 Mixins
@mixin modern-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  height: 40rpx;
  padding: 0 $space-2;
  border-radius: 9999rpx;
  font-size: $font-xs;
  font-weight: 700;
  color: $white;
  background: $error;
}

@mixin badge-primary {
  background: $primary-solid;
}

@mixin badge-success {
  background: $success;
}

@mixin badge-warning {
  background: $warning;
}