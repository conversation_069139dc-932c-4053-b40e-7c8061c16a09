require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedMessages() {
  try {
    console.log('🌱 开始创建消息和通知数据...');

    // 获取用户
    const users = await prisma.user.findMany();
    if (users.length === 0) {
      console.log('❌ 没有找到用户，请先运行 seed-complete-data.js');
      return;
    }

    // 创建消息
    const messages = await Promise.all([
      prisma.message.create({
        data: {
          content: '今天想吃红烧肉！',
          userId: users[1].id, // 爸爸
          read: false
        }
      }),
      prisma.message.create({
        data: {
          content: '明天能做点清淡的菜吗？',
          userId: users[2].id, // 妈妈
          read: false
        }
      }),
      prisma.message.create({
        data: {
          content: '小笼包做得真好吃！',
          userId: users[3].id, // 小明
          read: true
        }
      }),
      prisma.message.create({
        data: {
          content: '这周的菜单很棒，营养搭配很好',
          userId: users[1].id, // 爸爸
          read: true
        }
      }),
      prisma.message.create({
        data: {
          content: '能不能多做点汤？最近有点上火',
          userId: users[2].id, // 妈妈
          read: false
        }
      })
    ]);
    console.log('✅ 创建消息:', messages.length, '条');

    // 创建通知
    const notifications = await Promise.all([
      prisma.notification.create({
        data: {
          content: '今日菜单已更新，快来看看吧！',
          userId: users[0].id, // 楠楠
          read: false
        }
      }),
      prisma.notification.create({
        data: {
          content: '明天是周末，有什么想吃的菜吗？',
          userId: users[0].id, // 楠楠
          read: false
        }
      }),
      prisma.notification.create({
        data: {
          content: '感谢大家的支持，楠楠家厨越来越好了！',
          userId: users[0].id, // 楠楠
          read: true
        }
      }),
      prisma.notification.create({
        data: {
          content: '新增了几道川菜，喜欢吃辣的朋友有福了',
          userId: users[0].id, // 楠楠
          read: false
        }
      }),
      prisma.notification.create({
        data: {
          content: '本周菜单评价很高，下周继续保持！',
          userId: users[0].id, // 楠楠
          read: true
        }
      })
    ]);
    console.log('✅ 创建通知:', notifications.length, '条');

    console.log('\n🎉 消息和通知数据创建完成！');
    
    // 验证数据
    const messageCount = await prisma.message.count();
    const notificationCount = await prisma.notification.count();
    
    console.log('\n📊 数据统计:');
    console.log(`   消息: ${messageCount} 条`);
    console.log(`   通知: ${notificationCount} 条`);

  } catch (error) {
    console.error('❌ 创建消息数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedMessages();
