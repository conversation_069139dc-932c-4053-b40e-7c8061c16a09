.container {
  background: #121212;
  color: #fff;
  min-height: 100vh;
  padding: 30rpx 20rpx;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #00f2ea;
  text-align: center;
}

.icon-margin {
  margin-right: 16rpx;
}

/* 空购物篮样式 */
.empty-basket {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.empty-text {
  color: #b3e0f7;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.go-order-btn {
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  font-weight: bold;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
}

/* 订单已提交样式 */
.order-submitted {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.submitted-text {
  color: #00f2ea;
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 30rpx;
  margin-bottom: 40rpx;
}

.submitted-actions {
  display: flex;
  gap: 30rpx;
}

.view-menu-btn,
.continue-order-btn {
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.view-menu-btn {
  background: #00f2ea;
  color: #181a20;
}

.continue-order-btn {
  background: #fe2c55;
  color: #fff;
}

/* 订单内容样式 */
.order-content {
  display: flex;
  flex-direction: column;
}

.order-list {
  margin-bottom: 30rpx;
}

.order-item {
  background: linear-gradient(120deg, #23272f 60%, #23233b 100%);
  border-radius: 24rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 242, 234, 0.13);
  gap: 20rpx;
}

.order-item-img {
  width: 112rpx;
  height: 112rpx;
  border-radius: 20rpx;
  object-fit: cover;
  box-shadow: 0 4rpx 16rpx rgba(0, 242, 234, 0.2);
  border: 4rpx solid rgba(0, 242, 234, 0.2);
}

.order-item-info {
  flex: 1;
  min-width: 0;
}

.order-item-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 4rpx;
}

.order-item-desc {
  color: #b3e0f7;
  font-size: 26rpx;
  margin-bottom: 4rpx;
}

.order-item-remark {
  color: #ffd580;
  font-size: 26rpx;
  word-break: break-all;
}

.order-item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10rpx;
}

.order-item-count {
  color: #fe2c55;
  font-weight: bold;
  font-size: 32rpx;
}

.delete-btn {
  color: #fe2c55;
  font-size: 36rpx;
  padding: 10rpx;
}

/* 备注和用餐时间 */
.remark-section {
  margin: 30rpx 0;
}

.remark-label {
  color: #b3e0f7;
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

.remark-input {
  width: 100%;
  background: #232323;
  color: #fff;
  border: 2rpx solid #00f2ea;
  border-radius: 20rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  min-height: 120rpx;
  margin-bottom: 30rpx;
}

.time-picker {
  width: 100%;
  background: #232323;
  color: #fff;
  border: 2rpx solid #00f2ea;
  border-radius: 20rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.picker-value {
  color: #fff;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
}

/* 提交按钮 */
.submit-btn {
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  font-weight: bold;
  padding: 20rpx 0;
  border-radius: 40rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  text-align: center;
  margin-bottom: 30rpx;
}

/* 历史菜单入口 */
.history-entry {
  background: #20232a;
  border-radius: 20rpx;
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 242, 234, 0.13);
  margin-top: 10rpx;
}

.history-title {
  color: #00f2ea;
  font-weight: bold;
  font-size: 30rpx;
  display: flex;
  align-items: center;
}

/* 对话框样式 */
.custom-dialog {
  border-radius: 24rpx !important;
  overflow: hidden !important;
  width: 85% !important; /* 控制对话框宽度 */
  max-width: 600rpx !important;
}

.custom-dialog-title {
  padding: 0 !important;
  margin: 0 !important;
  height: 0 !important;
}

.custom-dialog-button {
  font-weight: bold !important;
  font-size: 30rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  flex: 1 !important;
  max-width: 45% !important; /* 控制按钮最大宽度 */
  padding: 0 !important;
  margin: 0 10rpx !important;
}

/* 修复按钮容器样式 */
:host {
  --dialog-width: 85% !important;
  --dialog-max-width: 600rpx !important;
}

/* 修复按钮区域样式 */
.van-dialog__footer {
  display: flex !important;
  justify-content: center !important;
  padding: 20rpx 0 !important;
}

/* 对话框内容样式 */
.dialog-content {
  padding: 40rpx 30rpx 30rpx;
  background: #f8f8f8;
  border-radius: 0 0 24rpx 24rpx;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  background: #fff;
  padding: 24rpx 0;
  border-radius: 16rpx;
  margin: -20rpx -10rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.dialog-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-left: 12rpx;
  text-align: center;
}

.dialog-items {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.dialog-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 10rpx;
  font-size: 30rpx;
  color: #333;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.dialog-item-name {
  display: flex;
  align-items: center;
}

.dialog-item-dot {
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #00f2ea;
  margin-right: 12rpx;
}

.dialog-item-count {
  color: #fe2c55;
  font-weight: bold;
}

.dialog-info {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.dialog-remark,
.dialog-time {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: flex;
}

.dialog-remark-label,
.dialog-time-label {
  font-weight: bold;
  color: #333;
  min-width: 120rpx;
}

.dialog-remark-content,
.dialog-time-content {
  flex: 1;
  word-break: break-all;
}
