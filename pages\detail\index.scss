/* 菜品详情页面 - Tailwind CSS 风格 */

.detail-container {
  background-color: #f9fafb;
  color: #111827;
  min-height: 100vh;
  padding: 32rpx 24rpx;
}

.detail-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  border-radius: 24rpx;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  margin: 32rpx auto 0 auto;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  max-width: 600rpx;
  position: relative;
  overflow: hidden;
}

.detail-img {
  width: 200rpx;
  height: 200rpx;
  border-radius: 24rpx;
  object-fit: cover;
  background-color: #111827;
  border: 4rpx solid #3b82f6;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  display: block;
  margin: 0 auto 24rpx auto;
}

/* 详情内容 */
.detail-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  text-align: center;
  margin-bottom: 16rpx;
}

.detail-remark {
  color: #d1d5db;
  font-size: 26rpx;
  text-align: center;
  margin-bottom: 32rpx;
}

.detail-section {
  margin-bottom: 24rpx;
}

.detail-label {
  color: #ec4899;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  gap: 8rpx;
}

.icon-margin {
  margin-right: 8rpx;
}

.detail-content {
  color: #f3f4f6;
  font-size: 26rpx;
  line-height: 1.6;
  background-color: #111827;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 8rpx;
  word-break: break-all;
  border: 1rpx solid #374151;
}

/* 返回按钮 */
.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32rpx auto 0 auto;
  background: linear-gradient(135deg, #ec4899, #3b82f6);
  color: #111827;
  font-weight: 600;
  padding: 16rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 160rpx;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}
