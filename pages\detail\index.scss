/* 现代化设计 */
@import "../../styles/modern-design.scss";

/* 菜品详情页面 - Tailwind CSS 风格 */

.detail-container {
  background-color: #f9fafb;
  color: #111827;
  min-height: 100vh;
  @extend .p-4 24rpx;
}

.detail-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @extend .rounded-lg;
  @extend .shadow-md;
  margin: 32rpx auto 0 auto;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  max-width: 600rpx;
  position: relative;
  overflow: hidden;
}

.detail-img {
  width: 200rpx;
  height: 200rpx;
  @extend .rounded-lg;
  object-fit: cover;
  @extend .bg-white;
  border: 4rpx solid #3b82f6;
  @extend .shadow-md;
  display: block;
  margin: 0 auto 24rpx auto;
}

/* 详情内容 */
.detail-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  text-align: center;
  @extend .mb-2;
}

.detail-remark {
  color: #d1d5db;
  font-size: 26rpx;
  text-align: center;
  @extend .mb-4;
}

.detail-section {
  @extend .mb-3;
}

.detail-label {
  @extend .text-primary;
  font-weight: 600;
  margin-bottom: 12rpx;
  @extend .flex;
  @extend .items-center;
  font-size: 28rpx;
  gap: 8rpx;
}

.icon-margin {
  margin-right: 8rpx;
}

.detail-content {
  color: #f3f4f6;
  font-size: 26rpx;
  line-height: 1.6;
  @extend .bg-white;
  @extend .rounded-sm;
  padding: 20rpx;
  margin-bottom: 8rpx;
  word-break: break-all;
  border: 1rpx solid #374151;
}

/* 返回按钮 */
.back-btn {
  @extend .flex;
  @extend .items-center;
  @extend .justify-center;
  margin: 32rpx auto 0 auto;
  @extend .modern-btn; @extend .btn-primary;
  color: #111827;
  font-weight: 600;
  @extend .p-2 32rpx;
  @extend .rounded-md;
  font-size: 28rpx;
  @extend .shadow-md;
  width: 160rpx;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    @extend .shadow-md;
  }
}
