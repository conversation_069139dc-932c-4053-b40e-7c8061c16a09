/* 菜品详情页面 - Tailwind CSS 风格 */

.detail-container {
  background-color: var(--gray-900);
  color: white;
  min-height: 100vh;
  padding: 32rpx 24rpx;
}

.detail-card {
  background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
  border-radius: 24rpx;
  box-shadow: var(--shadow-2xl);
  margin: 32rpx auto 0 auto;
  padding: 48rpx 32rpx;
  border: 2rpx solid var(--gray-600);
  max-width: 600rpx;
  position: relative;
  overflow: hidden;
}

.detail-img {
  width: 200rpx;
  height: 200rpx;
  border-radius: 24rpx;
  object-fit: cover;
  background-color: var(--gray-800);
  border: 4rpx solid var(--primary-500);
  box-shadow: var(--shadow-xl);
  display: block;
  margin: 0 auto 24rpx auto;
}

/* 详情内容 */
.detail-title {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--primary-500);
  text-align: center;
  margin-bottom: 16rpx;
}

.detail-remark {
  color: var(--gray-300);
  font-size: 26rpx;
  text-align: center;
  margin-bottom: 32rpx;
}

.detail-section {
  margin-bottom: 24rpx;
}

.detail-label {
  color: var(--pink-500);
  font-weight: 600;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  gap: 8rpx;
}

.icon-margin {
  margin-right: 8rpx;
}

.detail-content {
  color: var(--gray-100);
  font-size: 26rpx;
  line-height: 1.6;
  background-color: var(--gray-800);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 8rpx;
  word-break: break-all;
  border: 1rpx solid var(--gray-700);
}

/* 返回按钮 */
.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32rpx auto 0 auto;
  background: linear-gradient(135deg, var(--pink-500), var(--primary-500));
  color: white;
  font-weight: 600;
  padding: 16rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  box-shadow: var(--shadow-lg);
  width: 160rpx;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: var(--shadow-md);
  }
}
