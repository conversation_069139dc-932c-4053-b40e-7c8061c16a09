<template>
  <div
    class="h-full w-full border-solid border-2 border-transparent bg-[#dfe5ec] flex flex-wrap"
  >
    <div class="two w-[160px] h-[160px] m-20 bg-white">
      内部阴影 水平 垂直 模糊 扩散 颜色
    </div>
    <div class="one w-[160px] h-[160px] m-20 bg-white">
      水平 垂直 模糊 扩散 颜色
    </div>
    <div class="one1 w-[160px] h-[160px] m-20 bg-white" />
    <div class="one2 w-[160px] h-[160px] m-20 bg-white">光源从左上方打过来</div>
    <div
      class="one3 w-[160px] h-[160px] m-20 bg-white rounded-[50%] overflow-hidden"
    />
    <div
      class="w-[220px] h-[220px] m-20 bg-black flex items-center justify-center"
    >
      <div class="one4 w-[110px] h-[110px] rounded-[50%]" />
    </div>
    <p class="one5 color-transparent text-base">文字少一个扩散参数和inset</p>
    <div class="one6 color-transparent text-[20px]">文字3d</div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "BoxShadow"
});
</script>

<style scoped lang="scss">
.two {
  box-shadow: inset 25px 25px 10px 0 #89d26d;
}

.one {
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
}

.one1 {
  box-shadow: 8px 8px 8px rgba(0, 0, 0, 0.3);
}

.one2 {
  box-shadow:
    8px 8px 8px rgba(0, 0, 0, 0.3),
    -8px -8px 8px rgba(255, 255, 255, 0.7);
}

.one3 {
  box-shadow:
    inset 8px 8px 8px rgba(0, 0, 0, 0.3),
    inset -8px -8px 8px rgba(255, 255, 255, 0.7),
    -10px -10px 10px rgba(0, 0, 0, 0.4);
}

.one4 {
  box-shadow: 50px 0 0 0 rgba(255, 255, 0, 0.7);
}

.one5 {
  text-shadow: 0 0 6px #000;
}

.one6 {
  text-shadow:
    2px 2px 2px #ddd,
    4px 4px 2px #bbb,
    6px 6px 2px #999,
    8px 8px 2px #777,
    10px 10px 2px #555,
    12px 12px 2px #333,
    14px 14px 2px #111;
}
</style>
