<view class="container">
  <!-- 头部导航 -->
  <view class="page-header">
    <view class="header-left">
      <van-icon name="arrow-left" size="20px" bindtap="goBack" />
    </view>
    <view class="header-title">
      <van-icon name="shop-o" size="18px" />
      <text>点菜系统</text>
    </view>
    <view class="header-right">
      <view class="basket-mini" bindtap="goToBasket">
        <van-icon name="shopping-cart-o" size="18px" />
        <view
          class="basket-mini-count"
          wx:if="{{basketCount > 0}}"
          >{{basketCount}}</view
        >
      </view>
    </view>
  </view>

  <view class="dish-page">
    <!-- 左侧分类导航 -->
    <view class="side-nav">
      <view
        wx:for="{{categories}}"
        wx:key="type"
        class="nav-item {{currentType === item.type ? 'active' : ''}}"
        data-type="{{item.type}}"
        bindtap="switchCategory"
      >
        {{item.name}}
      </view>
    </view>

    <!-- 右侧菜品列表 -->
    <view class="food-list-area">
      <view class="food-list">
        <view
          wx:for="{{foodList}}"
          wx:key="id"
          class="food-card"
          data-id="{{item.id}}"
        >
          <image
            class="food-img"
            src="{{item.img}}"
            mode="aspectFill"
            lazy-load
          />
          <view class="food-info">
            <view class="food-name" bindtap="goToDetail" data-id="{{item.id}}">
              {{item.name}}
            </view>
            <view class="food-desc">{{item.remark}}</view>
            <view class="food-card-bottom">
              <view
                class="order-btn {{item.isAdding ? 'adding' : ''}}"
                bindtap="addToBasket"
                data-id="{{item.id}}"
                data-name="{{item.name}}"
                data-remark="{{item.remark}}"
                data-img="{{item.img}}"
                data-index="{{index}}"
                hover-class="order-btn-hover"
              >
                <van-icon
                  wx:if="{{item.isAdding}}"
                  name="success"
                  class="success-icon"
                />
                <text wx:else>点菜</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 购物篮悬浮按钮 -->
  <view class="basket-fab" bindtap="goToBasket">
    <van-icon name="shopping-cart-o" size="28px" color="#fff" />
    <view class="basket-count">{{basketCount}}</view>
  </view>
</view>
