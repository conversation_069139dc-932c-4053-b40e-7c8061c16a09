/* 现代化设计 */
@import "../../styles/miniprogram-design.scss";

/* 消息页面 - Tailwind CSS 风格 */

.container {
  @include page-container;
  @include page-container-safe;
}

.page-header {
  @include flex;
  @include justify-between;
  @include items-center;
  @include mb-4;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: $primary-solid;
  text-align: center;
  flex: 1;
}

.family-msg-btn {
  @include modern-btn; @include btn-primary;
  color: #111827;
  font-weight: 600;
  padding: 12rpx 24rpx;
  @include rounded-lg;
  font-size: 26rpx;
  @include flex;
  @include items-center;
  gap: 8rpx;
  @include shadow-md;
  transition: all 0.2s ease;
}

.family-msg-btn:active {
  transform: scale(0.95);
}

.icon-margin {
  margin-right: 8rpx;
}

/* 主卡片 */
.main-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @include rounded-lg;
  @include shadow-md;
  @include mb-4;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    @include modern-btn; @include btn-primary;
    @extend .rounded-lg 24rpx 0 0;
  }
}

/* 消息项 */
.notice-item {
  background: linear-gradient(135deg, white, #f3f4f6);
  @include rounded-md;
  @include shadow-md;
  @include p-3;
  margin-bottom: 20rpx;
  border: 1rpx solid #4b5563;
}

.notice-title {
  color: $primary-solid;
  font-weight: 600;
  font-size: 28rpx;
}

.notice-time {
  color: #9ca3af;
  font-size: 22rpx;
  margin-top: 8rpx;
}

.notice-form {
  @include flex;
  margin-top: 20rpx;
}

.notice-input {
  flex: 1;
  border: 2rpx solid #fe2c55;
  @extend .rounded-lg 0 0 24rpx;
  padding: 0 28rpx;
  font-size: 30rpx;
  background: #2a2a2a;
  color: #111827;
  height: 88rpx;
  line-height: 88rpx;
  box-sizing: border-box;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
  line-height: 88rpx;
}

.notice-send {
  @include flex;
  @include items-center;
  @include justify-center;
  width: 100rpx;
  border-radius: 0 24rpx 24rpx 0;
  font-size: 36rpx;
  background: #fe2c55;
  color: #111827;
}

.send-icon {
  font-size: 40rpx !important;
  font-weight: bold;
}


/* 消息页面特定样式 */
.message-list {
  @include flex;
  @include flex-col;
  @include gap-2;
}

.message-item {
  @include modern-card;
  @include card-flat;
  @include p-3;
}

.add-notice-form {
  @include modern-card;
  @include p-4;
  @include mb-4;
}