/* 消息页面 - Tailwind CSS 风格 */

.container {
  background-color: #111827;
  min-height: 100vh;
  padding: 32rpx 24rpx;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  text-align: center;
  flex: 1;
}

.family-msg-btn {
  background: linear-gradient(135deg, #ec4899, #3b82f6);
  color: white;
  font-weight: 600;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.family-msg-btn:active {
  transform: scale(0.95);
}

.icon-margin {
  margin-right: 8rpx;
}

/* 主卡片 */
.main-card {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 24rpx;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  margin-bottom: 32rpx;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #ec4899, #3b82f6);
    border-radius: 24rpx 24rpx 0 0;
  }
}

/* 消息项 */
.notice-item {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 16rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #4b5563;
}

.notice-title {
  color: #3b82f6;
  font-weight: 600;
  font-size: 28rpx;
}

.notice-time {
  color: #9ca3af;
  font-size: 22rpx;
  margin-top: 8rpx;
}

.notice-form {
  display: flex;
  margin-top: 20rpx;
}

.notice-input {
  flex: 1;
  border: 2rpx solid #fe2c55;
  border-radius: 24rpx 0 0 24rpx;
  padding: 0 28rpx;
  font-size: 30rpx;
  background: #2a2a2a;
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  box-sizing: border-box;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
  line-height: 88rpx;
}

.notice-send {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  border-radius: 0 24rpx 24rpx 0;
  font-size: 36rpx;
  background: #fe2c55;
  color: #fff;
}

.send-icon {
  font-size: 40rpx !important;
  font-weight: bold;
}
