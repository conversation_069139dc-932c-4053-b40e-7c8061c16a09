/* 现代化设计 */
@import "../../styles/modern-design.scss";

/* 消息页面 - Tailwind CSS 风格 */

.container {
  @extend .page-container;
  @extend .page-container-safe;
}

.page-header {
  @extend .flex;
  @extend .justify-between;
  @extend .items-center;
  @extend .mb-4;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  text-align: center;
  flex: 1;
}

.family-msg-btn {
  @extend .modern-btn; @extend .btn-primary;
  color: #111827;
  font-weight: 600;
  padding: 12rpx 24rpx;
  @extend .rounded-lg;
  font-size: 26rpx;
  @extend .flex;
  @extend .items-center;
  gap: 8rpx;
  @extend .shadow-md;
  transition: all 0.2s ease;
}

.family-msg-btn:active {
  transform: scale(0.95);
}

.icon-margin {
  margin-right: 8rpx;
}

/* 主卡片 */
.main-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @extend .rounded-lg;
  @extend .shadow-md;
  @extend .mb-4;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    @extend .modern-btn; @extend .btn-primary;
    @extend .rounded-lg 24rpx 0 0;
  }
}

/* 消息项 */
.notice-item {
  background: linear-gradient(135deg, white, #f3f4f6);
  @extend .rounded-md;
  @extend .shadow-md;
  @extend .p-3;
  margin-bottom: 20rpx;
  border: 1rpx solid #4b5563;
}

.notice-title {
  color: #3b82f6;
  font-weight: 600;
  font-size: 28rpx;
}

.notice-time {
  color: #9ca3af;
  font-size: 22rpx;
  margin-top: 8rpx;
}

.notice-form {
  @extend .flex;
  margin-top: 20rpx;
}

.notice-input {
  flex: 1;
  border: 2rpx solid #fe2c55;
  @extend .rounded-lg 0 0 24rpx;
  padding: 0 28rpx;
  font-size: 30rpx;
  background: #2a2a2a;
  color: #111827;
  height: 88rpx;
  line-height: 88rpx;
  box-sizing: border-box;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
  line-height: 88rpx;
}

.notice-send {
  @extend .flex;
  @extend .items-center;
  @extend .justify-center;
  width: 100rpx;
  border-radius: 0 24rpx 24rpx 0;
  font-size: 36rpx;
  background: #fe2c55;
  color: #111827;
}

.send-icon {
  font-size: 40rpx !important;
  font-weight: bold;
}


/* 消息页面特定样式 */
.message-list {
  @extend .flex;
  @extend .flex-col;
  @extend .gap-2;
}

.message-item {
  @extend .modern-card;
  @extend .card-flat;
  @extend .p-3;
}

.add-notice-form {
  @extend .modern-card;
  @extend .p-4;
  @extend .mb-4;
}