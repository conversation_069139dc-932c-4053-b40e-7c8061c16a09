/**
 * 订单相关 API
 */

// 订单数据
let orders = [];

// 处理订单相关请求
function handleRequest(req) {
  const { method, params, data } = req;
  const { id, subResource } = params;
  
  // 根据请求方法和路径处理不同的请求
  switch (method) {
    case 'GET':
      if (id) {
        // GET /orders/:id - 获取指定订单
        return getOrderById(id);
      } else if (subResource === 'today') {
        // GET /orders/today - 获取今日订单
        return getTodayOrders();
      } else {
        // GET /orders - 获取所有订单
        return getAllOrders();
      }
    
    case 'POST':
      // POST /orders - 创建新订单
      return createOrder(data);
      
    case 'PUT':
      if (id) {
        // PUT /orders/:id - 更新订单
        return updateOrder(id, data);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    case 'DELETE':
      if (id) {
        // DELETE /orders/:id - 删除订单
        return deleteOrder(id);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    default:
      return { code: 405, message: 'Method not allowed', data: null };
  }
}

// 获取所有订单
function getAllOrders() {
  return {
    code: 200,
    message: 'Success',
    data: orders
  };
}

// 获取指定订单
function getOrderById(id) {
  const order = orders.find(o => o.id == id);
  
  if (order) {
    return {
      code: 200,
      message: 'Success',
      data: order
    };
  }
  
  return {
    code: 404,
    message: 'Order not found',
    data: null
  };
}

// 获取今日订单
function getTodayOrders() {
  const today = new Date().toISOString().split('T')[0];
  const todayOrders = orders.filter(o => o.created_at.startsWith(today));
  
  return {
    code: 200,
    message: 'Success',
    data: todayOrders
  };
}

// 创建新订单
function createOrder(data) {
  if (!data || !data.dishes || !Array.isArray(data.dishes) || data.dishes.length === 0) {
    return {
      code: 400,
      message: 'Invalid order data',
      data: null
    };
  }
  
  const now = new Date();
  const newOrder = {
    id: orders.length + 1,
    user_id: data.user_id || 1,
    dishes: data.dishes,
    remark: data.remark || '',
    dining_time: data.dining_time || '',
    status: 'pending',
    created_at: now.toISOString(),
    updated_at: now.toISOString()
  };
  
  orders.push(newOrder);
  
  return {
    code: 201,
    message: 'Order created',
    data: newOrder
  };
}

// 更新订单
function updateOrder(id, data) {
  const index = orders.findIndex(o => o.id == id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Order not found',
      data: null
    };
  }
  
  const now = new Date();
  orders[index] = { 
    ...orders[index], 
    ...data,
    updated_at: now.toISOString()
  };
  
  return {
    code: 200,
    message: 'Order updated',
    data: orders[index]
  };
}

// 删除订单
function deleteOrder(id) {
  const index = orders.findIndex(o => o.id == id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Order not found',
      data: null
    };
  }
  
  const deletedOrder = orders[index];
  orders = orders.filter(o => o.id != id);
  
  return {
    code: 200,
    message: 'Order deleted',
    data: deletedOrder
  };
}

module.exports = {
  handleRequest
};
