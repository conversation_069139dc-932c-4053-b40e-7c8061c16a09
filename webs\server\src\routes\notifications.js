const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const {auth, adminAuth, familyHeadAuth} = require('../middlewares/auth');

// 获取当前用户的通知列表 (需要认证)
router.get('/', auth, notificationController.getNotifications);

// 获取未读通知数量 (需要认证)
router.get('/unread-count', auth, notificationController.getUnreadCount);

// 获取指定通知 (需要认证)
router.get('/:id', auth, notificationController.getNotificationById);

// 创建个人通知 (需要认证)
router.post('/', auth, notificationController.createNotification);

// 发送家庭通知 (需要家庭管理员权限)
router.post(
  '/family',
  auth,
  familyHeadAuth,
  notificationController.sendFamilyNotification
);

// 发送系统通知 (需要管理员权限)
router.post(
  '/system',
  auth,
  adminAuth,
  notificationController.sendSystemNotification
);

// 标记通知为已读 (需要认证)
router.put('/:id/read', auth, notificationController.markAsRead);

// 标记所有通知为已读 (需要认证)
router.put('/read-all', auth, notificationController.markAllAsRead);

// 更新通知 (需要认证)
router.put('/:id', auth, notificationController.updateNotification);

// 删除通知 (需要认证)
router.delete('/:id', auth, notificationController.deleteNotification);

module.exports = router;
