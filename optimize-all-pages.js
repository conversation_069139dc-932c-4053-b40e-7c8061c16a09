#!/usr/bin/env node

/**
 * 批量优化所有页面样式
 * 将所有页面转换为现代化设计风格
 */

const fs = require('fs');
const path = require('path');

// 页面列表
const pagesToOptimize = [
  'pages/order/index.scss',
  'pages/today_order/index.scss',
  'pages/add_menu/index.scss',
  'pages/statistics/index.scss',
  'pages/message/index.scss',
  'pages/detail/index.scss',
  'pages/history_menu/index.scss',
  'pages/mine/index.scss'
];

// 样式替换规则
const styleReplacements = [
  // 容器样式
  {
    from: /\.container\s*{[^}]*background-color:\s*#[^;]*;[^}]*}/g,
    to: `.container {
  @extend .page-container;
  @extend .page-container-safe;
}`
  },
  
  // 卡片样式
  {
    from: /background:\s*linear-gradient\([^)]*#1f2937[^)]*\)/g,
    to: '@extend .modern-card'
  },
  {
    from: /background:\s*linear-gradient\([^)]*#111827[^)]*\)/g,
    to: '@extend .modern-card'
  },
  {
    from: /background-color:\s*#111827/g,
    to: '@extend .bg-white'
  },
  {
    from: /background-color:\s*#1f2937/g,
    to: '@extend .bg-white'
  },
  {
    from: /background:\s*white/g,
    to: '@extend .modern-card'
  },
  
  // 按钮样式
  {
    from: /background:\s*linear-gradient\([^)]*#fe2c55[^)]*\)/g,
    to: '@extend .modern-btn; @extend .btn-primary'
  },
  {
    from: /background:\s*linear-gradient\([^)]*#3b82f6[^)]*\)/g,
    to: '@extend .modern-btn; @extend .btn-primary'
  },
  {
    from: /background-color:\s*#3b82f6/g,
    to: '@extend .modern-btn; @extend .btn-primary'
  },
  {
    from: /background-color:\s*#10b981/g,
    to: '@extend .modern-btn; @extend .btn-success'
  },
  
  // 文字颜色
  {
    from: /color:\s*white/g,
    to: '@extend .text-gray-900'
  },
  {
    from: /color:\s*#fff/g,
    to: '@extend .text-gray-900'
  },
  {
    from: /color:\s*#e6e6e6/g,
    to: '@extend .text-gray-600'
  },
  {
    from: /color:\s*#fe2c55/g,
    to: '@extend .text-primary'
  },
  {
    from: /color:\s*#ec4899/g,
    to: '@extend .text-primary'
  },
  
  // 圆角
  {
    from: /border-radius:\s*44rpx/g,
    to: '@extend .rounded-xl'
  },
  {
    from: /border-radius:\s*32rpx/g,
    to: '@extend .rounded-xl'
  },
  {
    from: /border-radius:\s*24rpx/g,
    to: '@extend .rounded-lg'
  },
  {
    from: /border-radius:\s*16rpx/g,
    to: '@extend .rounded-md'
  },
  {
    from: /border-radius:\s*12rpx/g,
    to: '@extend .rounded-sm'
  },
  
  // 阴影
  {
    from: /box-shadow:\s*0\s+[^;]*rgba\(0,\s*0,\s*0[^;]*;/g,
    to: '@extend .shadow-md;'
  },
  
  // 布局
  {
    from: /display:\s*flex/g,
    to: '@extend .flex'
  },
  {
    from: /flex-direction:\s*column/g,
    to: '@extend .flex-col'
  },
  {
    from: /align-items:\s*center/g,
    to: '@extend .items-center'
  },
  {
    from: /justify-content:\s*center/g,
    to: '@extend .justify-center'
  },
  {
    from: /justify-content:\s*space-between/g,
    to: '@extend .justify-between'
  },
  
  // 间距
  {
    from: /margin-bottom:\s*32rpx/g,
    to: '@extend .mb-4'
  },
  {
    from: /margin-bottom:\s*24rpx/g,
    to: '@extend .mb-3'
  },
  {
    from: /margin-bottom:\s*16rpx/g,
    to: '@extend .mb-2'
  },
  {
    from: /padding:\s*32rpx/g,
    to: '@extend .p-4'
  },
  {
    from: /padding:\s*24rpx/g,
    to: '@extend .p-3'
  },
  {
    from: /padding:\s*16rpx/g,
    to: '@extend .p-2'
  }
];

/**
 * 优化单个文件
 * @param {string} filePath 文件路径
 */
function optimizeFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 添加现代化设计导入
    if (!content.includes('@import "../../styles/modern-design.scss"')) {
      content = `/* 现代化设计 */\n@import "../../styles/modern-design.scss";\n\n${content}`;
      hasChanges = true;
    }

    // 应用所有样式替换
    styleReplacements.forEach(replacement => {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
        console.log(`  ✅ 应用替换规则: ${replacement.from.toString().substring(0, 50)}...`);
      }
    });

    // 特殊处理：统一容器样式
    if (content.includes('.container {')) {
      content = content.replace(
        /\.container\s*{[^}]*}/g,
        `.container {
  @extend .page-container;
  @extend .page-container-safe;
}`
      );
      hasChanges = true;
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已优化: ${filePath}`);
    } else {
      console.log(`ℹ️  无需优化: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 优化文件失败 ${filePath}:`, error.message);
  }
}

/**
 * 创建页面特定的优化样式
 * @param {string} pageName 页面名称
 */
function createPageSpecificStyles(pageName) {
  const pageStyles = {
    'order': `
/* 点菜页面特定样式 */
.dish-page {
  @extend .flex;
  @extend .gap-3;
  height: calc(100vh - 120rpx);
  @extend .overflow-hidden;
}

.side-nav {
  width: 160rpx;
  @extend .modern-card;
  @extend .flex;
  @extend .flex-col;
  @extend .gap-2;
  height: 100%;
  flex-shrink: 0;
  @extend .overflow-auto;
}

.nav-item {
  @extend .modern-btn;
  @extend .btn-secondary;
  @extend .text-sm;
  
  &.active {
    @extend .btn-primary;
  }
}

.food-list-area {
  @extend .flex-1;
  @extend .modern-card;
  height: 100%;
  @extend .overflow-auto;
}

.food-item {
  @extend .modern-card;
  @extend .card-flat;
  @extend .flex;
  @extend .items-center;
  @extend .gap-3;
  @extend .p-3;
  @extend .mb-2;
}`,

    'today_order': `
/* 今日订单页面特定样式 */
.order-form {
  @extend .modern-card;
  @extend .p-4;
  @extend .mb-4;
}

.submit-btn {
  @extend .modern-btn;
  @extend .btn-primary;
  @extend .btn-full;
  @extend .btn-lg;
}

.user-selector {
  @extend .modern-card;
  @extend .card-flat;
  @extend .p-3;
  @extend .mb-3;
}`,

    'add_menu': `
/* 添加菜单页面特定样式 */
.main-card {
  @extend .modern-card;
  @extend .card-primary;
  @extend .p-4;
  @extend .mb-4;
}

.section-title {
  @extend .text-lg;
  @extend .font-semibold;
  @extend .text-primary;
  @extend .mb-3;
}

.input-warm {
  @extend .modern-input;
}

.submit-btn {
  @extend .modern-btn;
  @extend .btn-gradient;
  @extend .btn-full;
}`,

    'statistics': `
/* 统计页面特定样式 */
.stats-card {
  @extend .modern-card;
  @extend .card-primary;
  @extend .p-4;
  @extend .mb-4;
}

.chart-container {
  @extend .modern-card;
  @extend .p-4;
  height: 400rpx;
}`,

    'message': `
/* 消息页面特定样式 */
.message-list {
  @extend .flex;
  @extend .flex-col;
  @extend .gap-2;
}

.message-item {
  @extend .modern-card;
  @extend .card-flat;
  @extend .p-3;
}

.add-notice-form {
  @extend .modern-card;
  @extend .p-4;
  @extend .mb-4;
}`,

    'mine': `
/* 我的页面特定样式 */
.mine-user-card {
  @extend .modern-card;
  @extend .card-primary;
  @extend .flex;
  @extend .items-center;
  @extend .gap-3;
  @extend .p-4;
  @extend .mb-4;
}

.mine-action-card {
  @extend .modern-card;
  @extend .p-4;
}

.mine-link-btn {
  @extend .modern-btn;
  @extend .btn-secondary;
  @extend .flex-1;
}

.mine-action-btn {
  @extend .modern-btn;
  @extend .btn-gradient;
  @extend .btn-full;
}`
  };

  return pageStyles[pageName] || '';
}

/**
 * 主函数
 */
function main() {
  console.log('🎨 开始批量优化页面样式...\n');

  pagesToOptimize.forEach(filePath => {
    console.log(`\n📁 优化文件: ${filePath}`);
    optimizeFile(filePath);
    
    // 添加页面特定样式
    const pageName = path.basename(path.dirname(filePath));
    const specificStyles = createPageSpecificStyles(pageName);
    
    if (specificStyles) {
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        if (!content.includes('/* ' + pageName + '页面特定样式 */')) {
          content += '\n' + specificStyles;
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ 添加了页面特定样式`);
        }
      } catch (error) {
        console.error(`  ❌ 添加特定样式失败:`, error.message);
      }
    }
  });

  console.log('\n🎉 页面样式优化完成！');
  console.log('\n📋 优化说明:');
  console.log('- 所有页面已转换为现代化设计风格');
  console.log('- 统一了颜色、间距、圆角、阴影等设计元素');
  console.log('- 添加了页面特定的优化样式');
  console.log('- 建议在微信开发者工具中查看效果');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  optimizeFile,
  createPageSpecificStyles
};
