#!/usr/bin/env node

/**
 * 检查所有页面中使用的 mixins 是否都已定义
 */

const fs = require('fs');

// 页面文件列表
const pageFiles = [
  'pages/home/<USER>',
  'pages/order/index.scss',
  'pages/today_order/index.scss',
  'pages/add_menu/index.scss',
  'pages/statistics/index.scss',
  'pages/message/index.scss',
  'pages/detail/index.scss',
  'pages/history_menu/index.scss',
  'pages/mine/index.scss'
];

// 设计系统文件
const designFile = 'styles/miniprogram-design.scss';

/**
 * 提取文件中使用的所有 mixins
 */
function extractUsedMixins(filePath) {
  if (!fs.existsSync(filePath)) {
    return [];
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const mixinMatches = content.match(/@include\s+([a-zA-Z0-9-_]+)/g);
  
  if (!mixinMatches) {
    return [];
  }

  return mixinMatches.map(match => match.replace('@include ', '').trim());
}

/**
 * 提取设计系统中定义的所有 mixins
 */
function extractDefinedMixins() {
  if (!fs.existsSync(designFile)) {
    return [];
  }

  const content = fs.readFileSync(designFile, 'utf8');
  const mixinMatches = content.match(/@mixin\s+([a-zA-Z0-9-_]+)/g);
  
  if (!mixinMatches) {
    return [];
  }

  return mixinMatches.map(match => match.replace('@mixin ', '').trim());
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 检查缺失的 mixins...\n');

  // 获取所有定义的 mixins
  const definedMixins = extractDefinedMixins();
  console.log(`✅ 设计系统中定义了 ${definedMixins.length} 个 mixins:`);
  console.log(definedMixins.sort().join(', '));

  // 收集所有使用的 mixins
  const allUsedMixins = new Set();
  const fileUsage = {};

  pageFiles.forEach(filePath => {
    const usedMixins = extractUsedMixins(filePath);
    fileUsage[filePath] = usedMixins;
    usedMixins.forEach(mixin => allUsedMixins.add(mixin));
  });

  console.log(`\n📊 页面中总共使用了 ${allUsedMixins.size} 个不同的 mixins:`);
  console.log([...allUsedMixins].sort().join(', '));

  // 检查缺失的 mixins
  const missingMixins = [...allUsedMixins].filter(mixin => !definedMixins.includes(mixin));

  if (missingMixins.length > 0) {
    console.log(`\n❌ 缺失的 mixins (${missingMixins.length}个):`);
    missingMixins.forEach(mixin => {
      console.log(`  - @mixin ${mixin}`);
      
      // 显示哪些文件使用了这个 mixin
      const usingFiles = [];
      Object.entries(fileUsage).forEach(([file, mixins]) => {
        if (mixins.includes(mixin)) {
          usingFiles.push(file);
        }
      });
      console.log(`    使用文件: ${usingFiles.join(', ')}`);
    });

    console.log(`\n🔧 需要添加的 mixins:`);
    missingMixins.forEach(mixin => {
      console.log(`@mixin ${mixin} {`);
      console.log(`  // TODO: 添加样式定义`);
      console.log(`}`);
      console.log('');
    });
  } else {
    console.log(`\n✅ 所有使用的 mixins 都已定义！`);
  }

  // 显示每个文件的使用情况
  console.log(`\n📁 各文件 mixin 使用情况:`);
  Object.entries(fileUsage).forEach(([file, mixins]) => {
    if (mixins.length > 0) {
      console.log(`\n${file}:`);
      const uniqueMixins = [...new Set(mixins)].sort();
      uniqueMixins.forEach(mixin => {
        const isDefined = definedMixins.includes(mixin);
        const status = isDefined ? '✅' : '❌';
        console.log(`  ${status} @include ${mixin}`);
      });
    }
  });

  return missingMixins.length === 0;
}

// 运行脚本
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { extractUsedMixins, extractDefinedMixins };
