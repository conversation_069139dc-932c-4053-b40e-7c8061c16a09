// 小程序测试环境设置

// 模拟微信小程序全局对象
global.wx = {
  // 存储相关
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  clearStorageSync: jest.fn(),
  
  // 网络请求
  request: jest.fn(),
  
  // 导航相关
  navigateTo: jest.fn(),
  navigateBack: jest.fn(),
  redirectTo: jest.fn(),
  switchTab: jest.fn(),
  reLaunch: jest.fn(),
  
  // 界面交互
  showToast: jest.fn(),
  showModal: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  showActionSheet: jest.fn(),
  
  // 媒体相关
  chooseImage: jest.fn(),
  previewImage: jest.fn(),
  
  // 用户信息
  getUserInfo: jest.fn(),
  login: jest.fn(),
  
  // 其他API
  getSystemInfo: jest.fn(),
  setNavigationBarTitle: jest.fn(),
  
  // 页面相关
  createSelectorQuery: jest.fn(() => ({
    select: jest.fn(() => ({
      boundingClientRect: jest.fn(() => ({
        exec: jest.fn()
      }))
    }))
  }))
};

// 模拟 Page 构造函数
global.Page = jest.fn((options) => {
  return {
    data: options.data || {},
    setData: jest.fn(function(data) {
      Object.assign(this.data, data);
    }),
    ...options
  };
});

// 模拟 Component 构造函数
global.Component = jest.fn((options) => {
  return {
    data: options.data || {},
    properties: options.properties || {},
    methods: options.methods || {},
    setData: jest.fn(function(data) {
      Object.assign(this.data, data);
    })
  };
});

// 模拟 App 构造函数
global.App = jest.fn((options) => {
  return {
    globalData: options.globalData || {},
    ...options
  };
});

// 模拟 getApp 函数
global.getApp = jest.fn(() => ({
  globalData: {}
}));

// 模拟 getCurrentPages 函数
global.getCurrentPages = jest.fn(() => []);

// 模拟 require 函数用于小程序模块
global.require = jest.fn((path) => {
  if (path.includes('api')) {
    return require('../services/api.js');
  }
  return {};
});

// 设置默认的 wx API 返回值
wx.getStorageSync.mockReturnValue('');
wx.getSystemInfo.mockImplementation((options) => {
  if (options.success) {
    options.success({
      model: 'iPhone',
      platform: 'ios',
      system: 'iOS 14.0',
      version: '8.0.0',
      windowWidth: 375,
      windowHeight: 667
    });
  }
});

// 模拟网络请求成功
wx.request.mockImplementation((options) => {
  setTimeout(() => {
    if (options.success) {
      options.success({
        statusCode: 200,
        data: { code: 200, data: {}, message: 'success' }
      });
    }
  }, 100);
});

// 模拟用户登录
wx.login.mockImplementation((options) => {
  if (options.success) {
    options.success({
      code: 'mock_code_123'
    });
  }
});

// 模拟获取用户信息
wx.getUserInfo.mockImplementation((options) => {
  if (options.success) {
    options.success({
      userInfo: {
        nickName: '测试用户',
        avatarUrl: 'https://example.com/avatar.jpg'
      }
    });
  }
});

// 模拟选择图片
wx.chooseImage.mockImplementation((options) => {
  if (options.success) {
    options.success({
      tempFilePaths: ['temp://image1.jpg']
    });
  }
});

console.log('小程序测试环境初始化完成');
