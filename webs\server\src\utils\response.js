/**
 * 成功响应
 * @param {Object} res - Express 响应对象
 * @param {Object} data - 响应数据
 * @param {string} message - 响应消息
 * @param {number} statusCode - HTTP 状态码
 */
const success = (res, data = null, message = 'Success', statusCode = 200) => {
  res.status(statusCode).json({
    code: statusCode,
    message,
    data
  });
};

/**
 * 错误响应
 * @param {Object} res - Express 响应对象
 * @param {string} message - 错误消息
 * @param {number} statusCode - HTTP 状态码
 * @param {Object} data - 额外的错误数据
 */
const error = (res, message = 'Error', statusCode = 400, data = null) => {
  res.status(statusCode).json({
    code: statusCode,
    message,
    data
  });
};

module.exports = {
  success,
  error
};
