const express = require('express');
const router = express.Router();
const menuController = require('../controllers/menuController');
const {auth, adminAuth} = require('../middlewares/auth');

// 获取菜单列表
router.get('/', menuController.getMenus);

// 获取菜品分类
router.get('/categories', menuController.getCategories);

// 获取推荐菜单
router.get('/recommended', menuController.getRecommendedMenu);

// 获取统计信息
router.get('/statistics', menuController.getStatistics);

// 获取今日菜单
router.get('/today', menuController.getTodayMenu);

// 获取历史菜单
router.get('/history', menuController.getHistoryMenus);

// 获取指定菜单
router.get('/:id', menuController.getMenuById);

// 创建菜单 (需要认证)
router.post('/', auth, menuController.createMenu);

// 更新菜单 (需要认证)
router.put('/:id', auth, menuController.updateMenu);

// 删除菜单 (需要管理员权限)
router.delete('/:id', auth, adminAuth, menuController.deleteMenu);

module.exports = router;
