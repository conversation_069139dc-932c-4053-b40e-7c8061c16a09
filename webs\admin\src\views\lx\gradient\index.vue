<template>
  <div class="bg-white">
    <ul v-for="(item, index) in data" :key="index">
      <li class="title">{{ item.title }}</li>
      <li
        v-for="(i, idx) in item.child"
        :key="idx"
        :style="{ ...i.style, backgroundImage: i.background }"
        :class="['inline-block', 'text-center', i.class]"
      >
        {{ i.text }}
      </li>
    </ul>
  </div>
  <p class="gradient_text">我是小例子aaa</p>
  <p class="linear-gradient-bg">我是小例子aaa</p>
  <span class="b_text">
    鼠标放入下面横向为了让更多的人认可自己，鸣人的目标是成为火影。整个故事就围绕鸣人的奋斗、成长，鸣人的伙伴们，以及这个忍者世界的各种争斗和阴谋展开。
  </span>
</template>

<script setup lang="ts">
import { ref } from "vue";

let str =
  "radial-gradient(circle closest-side at 13% 50%,red ,transparent),radial-gradient(circle closest-side at 90% 63%,blue, transparent),radial-gradient(circle closest-side at 60% 83%,#d59b15, transparent),radial-gradient(circle closest-corner at 51% -123%, pink, transparent),radial-gradient(circle at 35% 156%,#e73c7e 28%,transparent 35%)";
console.log(str);
const data = ref([
  {
    title: "线性渐变",
    des: "linear-gradient",
    child: [
      {
        text: "red to-bottom 默认",
        background: "linear-gradient(to bottom, red,blue)"
      },
      {
        text: "red to-right",
        background: "linear-gradient(to right, red,blue)"
      },
      { text: "red to-top", background: "linear-gradient(to top, red,blue)" },
      {
        text: "red to-top-left",
        background: "linear-gradient(to top left, red,blue)"
      },
      {
        text: "red to-bottom-left",
        background: "linear-gradient(to bottom left, red,blue,pink)"
      },
      {
        text: "0deg === toTop  浏览器0d是底,180d是上面",
        background: "linear-gradient( 0deg , red,blue)"
      },
      { text: "45deg", background: "linear-gradient( 45deg,red,blue)" },
      { text: "90deg", background: "linear-gradient( 90deg,red,blue)" },
      { text: "180deg", background: "linear-gradient( 180deg,red,blue)" },
      { text: "3600deg", background: "linear-gradient( 360deg,red,blue)" },
      {
        text: "0deg 百分数是占比",
        background: "linear-gradient( 0deg,#80cbc4 60%,#c1c1c1 )"
      }
    ]
  },
  {
    title: "径向渐变",
    des: "radial-gradient(shape size at position[xmt],color,color) shape关键字：colsest-side farthest-side closest-corner farthest-corner",
    child: [
      { text: "椭圆默认,中间向外", background: "radial-gradient(red,blue)" },
      { text: "ellipse椭圆", background: "radial-gradient(ellipse,red,blue)" },
      {
        text: "farthest-corner at 50% 50% 完整写法",
        background:
          "radial-gradient(ellipse farthest-corner at 50% 50% ,red,blue)"
      },
      {
        text: "circle圆",
        background: "radial-gradient(circle,red,green,blue)"
      },
      {
        text: "circle圆 设置xy位置",
        background:
          "radial-gradient(circle farthest-corner at 16% 46%,red,green,blue)"
      },
      {
        text: "渐变-底部",
        background: "radial-gradient(circle at bottom,red,green,blue)"
      },
      {
        text: "渐变-中间",
        background: "radial-gradient(circle at 50% 50%,red 10%,green,blue)"
      },
      {
        text: "两个渐变函数",
        background:
          "radial-gradient(circle at bottom,red,blue),radial-gradient(circle at top,pink,green)"
      },
      {
        text: "",
        background: str,
        style: {
          backgroundColor: "#321575",
          width: "300px !important"
        }
      },
      {
        text: "",
        background:
          "radial-gradient(circle closest-side at 13% 50%,#e73c7e ,transparent )" +
          ",radial-gradient(circle closest-side at 60% 83%,#d59b15, transparent)" +
          ",radial-gradient(circle closest-corner at 51% -123%, #ba5179, transparent)" +
          ",radial-gradient(circle at 35% 156%,#e73c7e 28%,transparent 35%)",
        style: {
          backgroundColor: "#321575",
          width: "300px !important",
          position: "relative",
          overflow: "hidden"
        },
        class: "radial-many"
      },
      {
        text: "对角渐变",
        background:
          "radial-gradient(circle at 116% -48%,#e73c7e 26%,transparent 35%)" +
          ",radial-gradient(circle at -14% 143%,#d59b15 26%, transparent 35%)",
        style: {
          backgroundColor: "#321575",
          width: "300px !important"
        }
      },
      {
        text: "半遮罩层",
        background:
          "radial-gradient(circle at 50% 138%, rgba(118, 47, 120, 0.7), rgba(66, 33, 117, 0.5))"
      }
    ]
  },
  {
    title: "圆锥渐变",
    des: "conic-gradient",
    child: [
      {
        text: "",
        background:
          "conic-gradient( red 36deg, orange 36deg 170deg, yellow 170deg)",
        style: { borderRadius: "50%" }
      },
      {
        text: "",
        background:
          "conic-gradient(from -45deg, royalblue 90deg, transparent 0deg)"
      }
    ]
  }
]);
</script>

<style scoped lang="scss">
ul li {
  width: 30vmin;
  height: 10vmin;
  margin: 25px 14px;
  padding: 16px;
  line-height: 5vmin;
  border-radius: 16px;
  text-align: center;
  text-transform: capitalize;
  color: white;
}

.title {
  width: 100%;
  height: 40px;
  margin: 0;
  padding: 0;
  font-size: 20px;
  color: red;
}

.radial-many::before {
  content: "";
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle closest-side at 90% 63%,
    #59cfe6,
    transparent
  );
  position: absolute;
  top: 0;
  left: 0;
  animation: radialCircle 5s linear infinite;
}

@keyframes radialCircle {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(100%);
  }
}

.gradient_text {
  background-image: linear-gradient(341deg, #23a6d5, #d52378);
  background-clip: text; //背景延申到哪里 这个属性不可丢失
  -webkit-text-fill-color: transparent; //文本填充颜色，不适用此属性，则使用color颜色值
  font-size: 64px;
  letter-spacing: -0.5px;
  font-weight: bold;
  width: 80vmin;
}

.linear-gradient-bg {
  background-image: url("https://picsum.photos/200/300");
  background-repeat: no-repeat;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 40px;
  font-weight: bold;
  letter-spacing: -0.5px;
  width: 40vmin;
}
//文字下换线 -- 原理背景
.b_text {
  padding: 0;
  color: #333;
  line-height: 24px;
  background: linear-gradient(to right, #ff5f57, #28c840) no-repeat right bottom;
  background-size: 0px 2px;
  transition: background-size 1s ease;
  &:hover {
    background-size: 100% 2px;
    background-position: left bottom;
  }
}
</style>
