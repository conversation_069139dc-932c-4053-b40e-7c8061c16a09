require('dotenv').config();
const {spawn} = require('child_process');
const {PrismaClient} = require('@prisma/client');

const prisma = new PrismaClient();

async function quickStart() {
  console.log('🚀 楠楠家厨数据库快速启动...\n');

  try {
    // 1. 测试数据库连接
    console.log('1️⃣ 测试数据库连接...');
    await prisma.$connect();
    console.log('   ✅ 数据库连接成功\n');

    // 2. 检查数据状态
    console.log('2️⃣ 检查数据状态...');
    const userCount = await prisma.user.count();
    const dishCount = await prisma.dish.count();
    const menuCount = await prisma.menu.count();

    console.log(`   📊 用户: ${userCount} 条`);
    console.log(`   📊 菜品: ${dishCount} 条`);
    console.log(`   📊 菜单: ${menuCount} 条\n`);

    // 3. 检查 Prisma 客户端
    console.log('3️⃣ 检查 Prisma 客户端...');
    try {
      const fs = require('fs');
      const path = require('path');
      const clientPath = path.join(__dirname, '../node_modules/.prisma/client');
      if (fs.existsSync(clientPath)) {
        console.log('   ✅ Prisma 客户端已存在\n');
      } else {
        console.log('   ⚠️  Prisma 客户端不存在，尝试生成...');
        try {
          const generate = spawn('npm', ['run', 'generate'], {
            stdio: 'inherit',
            shell: true
          });

          await new Promise((resolve, reject) => {
            generate.on('close', code => {
              if (code === 0) {
                console.log('   ✅ Prisma 客户端生成成功\n');
                resolve();
              } else {
                console.log('   ⚠️  生成失败，但继续启动服务\n');
                resolve(); // 不阻止启动
              }
            });
          });
        } catch (error) {
          console.log('   ⚠️  生成失败，但继续启动服务\n');
        }
      }
    } catch (error) {
      console.log('   ⚠️  无法检查客户端状态，继续启动\n');
    }

    // 4. 启动服务
    console.log('4️⃣ 启动开发服务器...');
    console.log('   🌐 后端服务: http://localhost:3000');
    console.log('   🗄️ Prisma Studio: http://localhost:5555');
    console.log('   📝 按 Ctrl+C 停止服务\n');

    // 启动开发服务器
    const dev = spawn('npm', ['run', 'dev'], {
      stdio: 'inherit',
      shell: true
    });

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 正在停止服务...');
      dev.kill('SIGINT');
      process.exit(0);
    });
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

quickStart();
