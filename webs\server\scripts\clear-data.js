require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearData() {
  try {
    console.log('🗑️ 开始清空数据...');
    
    const tables = ['menuItem', 'menu', 'dish', 'category', 'order', 'message', 'notification', 'user'];
    
    for (const table of tables) {
      try {
        const result = await prisma[table].deleteMany();
        console.log(`   清空 ${table}: ${result.count} 条记录`);
      } catch (error) {
        console.log(`   清空 ${table} 失败:`, error.message);
      }
    }
    
    console.log('🎉 数据清空完成！');
    
  } catch (error) {
    console.error('❌ 清空失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearData();
