import axios from 'axios';
import {ElMessage} from 'element-plus';
import {useUserStore} from '@/stores/user';

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加token
    const userStore = useUserStore();
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`;
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      };
    }

    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    const {data} = response;

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response;
    }

    // 统一处理响应
    if (data.code === 200 || data.success === true) {
      return data;
    } else {
      ElMessage.error(data.message || '请求失败');
      return Promise.reject(new Error(data.message || '请求失败'));
    }
  },
  error => {
    console.error('响应错误:', error);

    // 处理不同的错误状态码
    if (error.response) {
      const {status, data} = error.response;

      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录');
          const userStore = useUserStore();
          userStore.logout();
          // 使用router跳转而不是window.location
          import('@/router').then(({default: router}) => {
            router.push('/login');
          });
          break;
        case 403:
          ElMessage.error('没有权限访问');
          break;
        case 404:
          ElMessage.error('请求的资源不存在');
          break;
        case 500:
          ElMessage.error('服务器内部错误');
          break;
        default:
          ElMessage.error(data?.message || '请求失败');
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查网络连接');
    } else {
      ElMessage.error('请求配置错误');
    }

    return Promise.reject(error);
  }
);

// 封装常用的请求方法
export const http = {
  /**
   * GET请求
   * @param {string} url 请求地址
   * @param {object} params 请求参数
   * @param {object} config 额外配置
   */
  get(url, params = {}, config = {}) {
    return request.get(url, {
      params,
      ...config
    });
  },

  /**
   * POST请求
   * @param {string} url 请求地址
   * @param {object} data 请求数据
   * @param {object} config 额外配置
   */
  post(url, data = {}, config = {}) {
    return request.post(url, data, config);
  },

  /**
   * PUT请求
   * @param {string} url 请求地址
   * @param {object} data 请求数据
   * @param {object} config 额外配置
   */
  put(url, data = {}, config = {}) {
    return request.put(url, data, config);
  },

  /**
   * DELETE请求
   * @param {string} url 请求地址
   * @param {object} params 请求参数
   * @param {object} config 额外配置
   */
  delete(url, params = {}, config = {}) {
    return request.delete(url, {
      params,
      ...config
    });
  },

  /**
   * 文件上传
   * @param {string} url 上传地址
   * @param {FormData} formData 表单数据
   * @param {object} config 额外配置
   */
  upload(url, formData, config = {}) {
    return request.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    });
  },

  /**
   * 文件下载
   * @param {string} url 下载地址
   * @param {object} params 请求参数
   * @param {string} filename 文件名
   */
  download(url, params = {}, filename = '') {
    return request
      .get(url, {
        params,
        responseType: 'blob'
      })
      .then(response => {
        // 创建下载链接
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        return response;
      });
  },

  /**
   * 批量请求
   * @param {Array} requests 请求数组
   */
  all(requests) {
    return axios.all(requests);
  },

  /**
   * 并发请求
   * @param {Array} requests 请求数组
   * @param {number} limit 并发限制
   */
  async concurrent(requests, limit = 5) {
    const results = [];
    const executing = [];

    for (const request of requests) {
      const promise = Promise.resolve(request).then(result => {
        executing.splice(executing.indexOf(promise), 1);
        return result;
      });

      results.push(promise);

      if (requests.length >= limit) {
        executing.push(promise);

        if (executing.length >= limit) {
          await Promise.race(executing);
        }
      }
    }

    return Promise.all(results);
  }
};

// 请求取消功能
export const CancelToken = axios.CancelToken;
export const isCancel = axios.isCancel;

// 创建取消令牌
export function createCancelToken() {
  return CancelToken.source();
}

// 请求重试功能
export function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
  return new Promise((resolve, reject) => {
    let retries = 0;

    function attempt() {
      requestFn()
        .then(resolve)
        .catch(error => {
          if (retries < maxRetries && !isCancel(error)) {
            retries++;
            setTimeout(attempt, delay * retries);
          } else {
            reject(error);
          }
        });
    }

    attempt();
  });
}

// 请求缓存功能
const requestCache = new Map();

export function cachedRequest(key, requestFn, ttl = 5 * 60 * 1000) {
  const cached = requestCache.get(key);

  if (cached && Date.now() - cached.timestamp < ttl) {
    return Promise.resolve(cached.data);
  }

  return requestFn().then(data => {
    requestCache.set(key, {
      data,
      timestamp: Date.now()
    });
    return data;
  });
}

// 清除缓存
export function clearCache(key) {
  if (key) {
    requestCache.delete(key);
  } else {
    requestCache.clear();
  }
}

export default request;
