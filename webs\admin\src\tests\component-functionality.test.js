// 组件功能测试
import { describe, it, expect, beforeAll, beforeEach, vi } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import axios from 'axios'

// 导入组件
import Dashboard from '@/views/dashboard/index.vue'
import Login from '@/views/login/index.vue'

// 配置axios
axios.defaults.baseURL = 'http://localhost:3000'

const TEST_USER = {
  username: '13800138000',
  password: '123456'
}

let authToken = null

// 获取认证token
async function getAuthToken() {
  try {
    const response = await axios.post('/api/auth/login', {
      username: TEST_USER.username,
      password: TEST_USER.password,
      loginType: 'password'
    })
    
    if (response.data.code === 200) {
      return response.data.data.token
    }
    throw new Error('登录失败')
  } catch (error) {
    throw new Error(`无法获取认证token: ${error.message}`)
  }
}

// 创建测试用的路由
function createTestRouter() {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', redirect: '/dashboard' },
      { path: '/dashboard', name: 'Dashboard', component: Dashboard },
      { path: '/login', name: 'Login', component: Login }
    ]
  })
}

// 创建组件包装器
function createWrapper(component, options = {}) {
  const router = createTestRouter()
  const pinia = createPinia()
  
  return mount(component, {
    global: {
      plugins: [router, pinia],
      stubs: {
        'el-row': true,
        'el-col': true,
        'el-card': true,
        'el-button': true,
        'el-form': true,
        'el-form-item': true,
        'el-input': true,
        'el-select': true,
        'el-option': true,
        'el-table': true,
        'el-table-column': true,
        'el-pagination': true,
        'el-dialog': true,
        'el-message-box': true,
        'el-loading': true,
        'el-scrollbar': true,
        'el-backtop': true,
        'router-view': true,
        'router-link': true
      },
      mocks: {
        $router: router,
        $route: router.currentRoute
      }
    },
    ...options
  })
}

describe('组件功能测试', () => {
  beforeAll(async () => {
    try {
      authToken = await getAuthToken()
      axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
      console.log('✅ 测试环境初始化成功')
    } catch (error) {
      console.error('❌ 测试环境初始化失败:', error.message)
      throw error
    }
  })

  beforeEach(() => {
    // 设置localStorage
    window.localStorage.getItem = vi.fn(() => authToken)
    window.localStorage.setItem = vi.fn()
    window.localStorage.removeItem = vi.fn()
  })

  describe('登录组件测试', () => {
    it('登录组件应该正常渲染', () => {
      const wrapper = createWrapper(Login)
      expect(wrapper.exists()).toBe(true)
    })

    it('登录表单应该包含必要的字段', () => {
      const wrapper = createWrapper(Login)
      
      // 检查是否有用户名和密码输入框
      const usernameInput = wrapper.find('input[type="text"]')
      const passwordInput = wrapper.find('input[type="password"]')
      
      expect(usernameInput.exists() || wrapper.find('[placeholder*="用户名"]').exists()).toBe(true)
      expect(passwordInput.exists() || wrapper.find('[placeholder*="密码"]').exists()).toBe(true)
    })
  })

  describe('仪表盘组件测试', () => {
    it('仪表盘组件应该正常渲染', async () => {
      const wrapper = createWrapper(Dashboard)
      expect(wrapper.exists()).toBe(true)
    })

    it('仪表盘应该显示统计卡片', async () => {
      const wrapper = createWrapper(Dashboard)
      await flushPromises()
      
      // 检查是否有统计相关的元素
      const hasStatsElements = wrapper.find('.dashboard').exists() || 
                              wrapper.find('[class*="stat"]').exists() ||
                              wrapper.find('[class*="card"]').exists()
      
      expect(hasStatsElements).toBe(true)
    })

    it('仪表盘应该能够加载数据', async () => {
      const wrapper = createWrapper(Dashboard)
      
      // 等待组件挂载和数据加载
      await flushPromises()
      
      // 检查组件是否没有抛出错误
      expect(wrapper.vm).toBeDefined()
    })
  })

  describe('API集成测试', () => {
    it('应该能够获取菜单统计数据', async () => {
      try {
        const response = await axios.get('/api/menus/statistics')
        expect(response.status).toBe(200)
        expect(response.data).toBeDefined()
      } catch (error) {
        console.error('获取菜单统计失败:', error.message)
        throw error
      }
    })

    it('应该能够获取今日菜单', async () => {
      try {
        const response = await axios.get('/api/menus/today')
        expect(response.status).toBe(200)
        expect(response.data).toBeDefined()
      } catch (error) {
        console.error('获取今日菜单失败:', error.message)
        throw error
      }
    })

    it('应该能够获取订单列表', async () => {
      try {
        const response = await axios.get('/api/orders')
        expect(response.status).toBe(200)
        expect(response.data).toBeDefined()
      } catch (error) {
        console.error('获取订单列表失败:', error.message)
        throw error
      }
    })

    it('应该能够获取用户列表', async () => {
      try {
        const response = await axios.get('/api/users')
        expect(response.status).toBe(200)
        expect(response.data).toBeDefined()
      } catch (error) {
        console.error('获取用户列表失败:', error.message)
        throw error
      }
    })

    it('应该能够获取消息列表', async () => {
      try {
        const response = await axios.get('/api/messages')
        expect(response.status).toBe(200)
        expect(response.data).toBeDefined()
      } catch (error) {
        console.error('获取消息列表失败:', error.message)
        throw error
      }
    })
  })

  describe('错误处理测试', () => {
    it('应该能够处理API错误', async () => {
      try {
        // 尝试访问不存在的端点
        await axios.get('/api/nonexistent')
      } catch (error) {
        expect(error.response.status).toBe(404)
      }
    })

    it('应该能够处理未授权访问', async () => {
      // 临时移除授权头
      const originalAuth = axios.defaults.headers.common['Authorization']
      delete axios.defaults.headers.common['Authorization']
      
      try {
        await axios.get('/api/users')
      } catch (error) {
        expect(error.response.status).toBe(401)
      } finally {
        // 恢复授权头
        axios.defaults.headers.common['Authorization'] = originalAuth
      }
    })
  })

  describe('数据完整性测试', () => {
    it('菜单数据应该有正确的结构', async () => {
      try {
        const response = await axios.get('/api/menus')
        expect(response.data).toHaveProperty('code')
        expect(response.data).toHaveProperty('data')
      } catch (error) {
        console.warn('菜单数据测试跳过:', error.message)
      }
    })

    it('订单数据应该有正确的结构', async () => {
      try {
        const response = await axios.get('/api/orders')
        expect(response.data).toHaveProperty('code')
        expect(response.data).toHaveProperty('data')
      } catch (error) {
        console.warn('订单数据测试跳过:', error.message)
      }
    })
  })
})
