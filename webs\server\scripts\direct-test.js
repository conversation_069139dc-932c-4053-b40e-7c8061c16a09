// 直接测试 GitHub API 上传图片
const fs = require('fs');
const path = require('path');
const https = require('https');

// GitHub API 配置
const config = {
  token: "****************************************",
  repo: "/repos/2497462726/picx-images-hosting/contents/",
  baseUrl: "api.github.com"
};

// 创建一个简单的测试图片
function createTestImage() {
  // 创建一个简单的 1x1 像素的黑色 PNG 图片
  const buffer = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
    0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);
  
  // 保存到临时文件
  const tempPath = path.join(__dirname, 'test.png');
  fs.writeFileSync(tempPath, buffer);
  console.log(`创建测试图片: ${tempPath}`);
  
  return { path: tempPath, buffer };
}

// 直接使用 HTTPS 模块上传图片到 GitHub
function uploadToGitHub(imageBuffer, filename) {
  return new Promise((resolve, reject) => {
    // 生成唯一文件名
    const uniqueFilename = `${Date.now()}.png`;
    
    // 将图片转换为 Base64
    const content = imageBuffer.toString('base64');
    
    // 构建请求数据
    const data = JSON.stringify({
      message: 'Upload image via test script',
      content,
      branch: 'master'
    });
    
    // 构建请求选项
    const options = {
      hostname: config.baseUrl,
      path: `${config.repo}${uniqueFilename}`,
      method: 'PUT',
      headers: {
        'Authorization': `token ${config.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Content-Length': data.length,
        'User-Agent': 'Node.js Test Script'
      }
    };
    
    console.log(`请求URL: https://${options.hostname}${options.path}`);
    console.log('请求头:', options.headers);
    
    // 发送请求
    const req = https.request(options, (res) => {
      console.log(`状态码: ${res.statusCode}`);
      console.log(`响应头: ${JSON.stringify(res.headers)}`);
      
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          console.log('响应数据:', JSON.stringify(parsedData, null, 2));
          
          if (res.statusCode === 201) {
            // 构建 CDN URL
            const cdnUrl = `https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting@master/${uniqueFilename}`;
            resolve(cdnUrl);
          } else {
            reject(new Error(`GitHub API 返回状态码 ${res.statusCode}`));
          }
        } catch (e) {
          console.error('解析响应数据失败:', e);
          reject(e);
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('请求错误:', error);
      reject(error);
    });
    
    // 写入请求数据
    req.write(data);
    req.end();
  });
}

// 测试图片上传功能
async function testImageUpload() {
  try {
    console.log('开始测试图片上传...');
    
    // 创建测试图片
    const testImage = createTestImage();
    
    // 上传图片
    console.log('正在上传图片...');
    const imageUrl = await uploadToGitHub(testImage.buffer, 'test.png');
    
    console.log('图片上传成功!');
    console.log(`图片URL: ${imageUrl}`);
    
    // 清理临时文件
    fs.unlinkSync(testImage.path);
  } catch (error) {
    console.error('图片上传测试失败:', error);
  }
}

// 执行测试
testImageUpload();
