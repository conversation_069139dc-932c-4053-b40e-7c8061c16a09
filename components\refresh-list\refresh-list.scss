/* 刷新列表组件样式 - Tailwind CSS 风格 */
.refresh-list-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.refresh-scroll-view {
  width: 100%;
  height: 100%;
}

/* 自定义下拉刷新样式 */
.custom-refresher {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
  background-color: #f9fafb;
}

.refresher-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.refresher-icon {
  margin-bottom: 8rpx;
  transition: transform 0.3s ease;
}

.refresher-icon.rotating {
  transform: rotate(180deg);
}

.refresher-text {
  font-size: 24rpx;
  color: #4b5563;
}

/* 列表内容 */
.list-content {
  min-height: 100%;
}

/* 加载更多容器 */
.load-more-container {
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.load-more-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #6b7280;
}

.load-more-text.no-more {
  color: #9ca3af;
}

.load-more-text.error {
  color: #ef4444;
  text-decoration: underline;
  cursor: pointer;
}

.load-more-text.error:active {
  color: #dc2626;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 64rpx 32rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.empty-action {
  margin-top: 16rpx;
}

/* 首次加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 64rpx 32rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #4b5563;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.list-content .list-item {
  animation: fadeIn 0.3s ease-out;
}

/* 下拉刷新动画 */
@keyframes refreshRotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.refresher-icon.rotating {
  animation: refreshRotate 1s linear infinite;
}

/* 小屏幕适配样式 */
.empty-container-small {
  min-height: 50vh;
  padding: 48rpx 24rpx;
}

.empty-image-small {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 24rpx;
}

.empty-text-small {
  font-size: 26rpx;
  margin-bottom: 24rpx;
}

.loading-container-small {
  min-height: 50vh;
  padding: 48rpx 24rpx;
}

.load-more-container-small {
  padding: 24rpx;
}
