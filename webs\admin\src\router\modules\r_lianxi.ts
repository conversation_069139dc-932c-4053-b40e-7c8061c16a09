import { $t } from "@/plugins/i18/i18n";
import { list } from "@/router/enums";

export default {
  path: "/lx",
  redirect: "/lx/one",
  meta: {
    icon: "listCheck",
    title: $t("menus.hslianxi"),
    rank: list
  },
  children: [
    {
      path: "/lx/card",
      name: "Gradient",
      component: () => import("@/views/lx/gradient/index.vue"),
      meta: {
        icon: "card",
        title: "练习背景",
        showParent: true
      }
    },
    {
      path: "/lx/shadow",
      name: "BoxShadow",
      component: () => import("@/views/lx/box-shadow/index.vue"),
      meta: {
        icon: "card",
        title: "练习阴影",
        showParent: true
      }
    },
    {
      path: "/lx/fragment",
      name: "Fragment",
      component: () => import("@/views/lx/fragment/index.vue"),
      meta: {
        icon: "card",
        title: "碎片化",
        showParent: true
      }
    },
    {
      path: "/lx/antX6",
      name: "Fragment",
      component: () => import("@/views/lx/antX6/index.vue"),
      meta: {
        icon: "card",
        title: "antx6",
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
