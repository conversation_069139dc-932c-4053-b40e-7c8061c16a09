#!/usr/bin/env node

/**
 * 修复小程序主题颜色
 * 将所有页面从黑色主题改为明亮主题
 */

const fs = require('fs');
const path = require('path');

// 颜色替换映射
const colorReplacements = [
  // 背景色替换
  { from: 'background-color: #111827', to: 'background-color: #f9fafb' },
  { from: 'background-color: #1f2937', to: 'background-color: white' },
  { from: 'background-color: #374151', to: 'background-color: #f3f4f6' },
  { from: 'background: #121212', to: 'background: #f9fafb' },
  { from: 'background: #181a20', to: 'background: #f3f4f6' },
  { from: 'background: #23272f', to: 'background: white' },
  { from: 'background: #232323', to: 'background: #f3f4f6' },
  
  // 渐变背景替换
  { from: 'linear-gradient(135deg, #1f2937, #374151)', to: 'linear-gradient(135deg, white, #f3f4f6)' },
  { from: 'linear-gradient(120deg, #1f1f1f 60%, #2a2a2a 100%)', to: 'linear-gradient(120deg, white 60%, #f9fafb 100%)' },
  { from: 'linear-gradient(135deg, #23272f, #23233b)', to: 'linear-gradient(135deg, white, #f9fafb)' },
  
  // 文字颜色替换
  { from: 'color: white', to: 'color: #111827' },
  { from: 'color: #fff', to: 'color: #111827' },
  { from: 'color: #f5f6fa', to: 'color: #374151' },
  { from: 'color: #e6e6e6', to: 'color: #6b7280' },
  { from: 'color: #b3e0f7', to: 'color: #4b5563' },
  
  // 边框颜色
  { from: 'border: 3rpx solid #232323', to: 'border: 2rpx solid #e5e7eb' },
  { from: 'border: 2rpx solid #232323', to: 'border: 2rpx solid #e5e7eb' },
  { from: 'border-color: #232323', to: 'border-color: #e5e7eb' },
];

// 需要处理的文件列表
const filesToProcess = [
  'pages/order/index.scss',
  'pages/today_order/index.scss', 
  'pages/add_menu/index.scss',
  'pages/statistics/index.scss',
  'pages/message/index.scss',
  'pages/detail/index.scss',
  'pages/history_menu/index.scss',
  'pages/mine/index.scss'
];

/**
 * 替换文件中的颜色
 * @param {string} filePath 文件路径
 */
function replaceColors(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 应用所有颜色替换
    colorReplacements.forEach(replacement => {
      if (content.includes(replacement.from)) {
        content = content.replace(new RegExp(replacement.from.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), 'g'), replacement.to);
        hasChanges = true;
        console.log(`  ✅ 替换: ${replacement.from} -> ${replacement.to}`);
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
    } else {
      console.log(`ℹ️  无需更新: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🎨 开始修复主题颜色...\n');

  filesToProcess.forEach(filePath => {
    console.log(`\n📁 处理文件: ${filePath}`);
    replaceColors(filePath);
  });

  console.log('\n🎉 主题颜色修复完成！');
  console.log('\n📋 修复说明:');
  console.log('- 所有页面已从黑色主题改为明亮主题');
  console.log('- 背景色: 深色 -> 浅色');
  console.log('- 文字色: 白色 -> 深色');
  console.log('- 边框色: 深色 -> 浅色');
  console.log('- 建议在微信开发者工具中查看效果');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  replaceColors,
  colorReplacements
};
