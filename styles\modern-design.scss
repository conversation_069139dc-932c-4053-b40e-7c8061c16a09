/* 现代化设计系统 - 小程序专用 */

// 🎨 设计令牌 (Design Tokens)

// 主色调 - 渐变蓝紫
$primary-start: #4f46e5; // 靛蓝
$primary-end: #7c3aed; // 紫色
$primary-solid: #6366f1; // 中间色

// 辅助色系
$secondary: #f59e0b; // 温暖橙
$accent: #ec4899; // 粉红
$success: #10b981; // 翠绿
$warning: #f59e0b; // 橙色
$error: #ef4444; // 红色
$info: #3b82f6; // 蓝色

// 中性色系 - 更丰富的层次
$white: #ffffff;
$gray-50: #f8fafc;
$gray-100: #f1f5f9;
$gray-200: #e2e8f0;
$gray-300: #cbd5e1;
$gray-400: #94a3b8;
$gray-500: #64748b;
$gray-600: #475569;
$gray-700: #334155;
$gray-800: #1e293b;
$gray-900: #0f172a;

// 间距系统 - 8pt 网格
$space-1: 8rpx; // 0.5rem
$space-2: 16rpx; // 1rem
$space-3: 24rpx; // 1.5rem
$space-4: 32rpx; // 2rem
$space-5: 40rpx; // 2.5rem
$space-6: 48rpx; // 3rem
$space-8: 64rpx; // 4rem
$space-10: 80rpx; // 5rem

// 圆角系统
$radius-xs: 8rpx;
$radius-sm: 12rpx;
$radius-md: 16rpx;
$radius-lg: 20rpx;
$radius-xl: 24rpx;
$radius-2xl: 32rpx;
$radius-full: 9999rpx;

// 阴影系统 - 更自然的阴影
$shadow-xs: 0 2rpx 8rpx rgba(15, 23, 42, 0.04);
$shadow-sm: 0 2rpx 16rpx rgba(15, 23, 42, 0.06);
$shadow-md: 0 8rpx 32rpx rgba(15, 23, 42, 0.08);
$shadow-lg: 0 16rpx 48rpx rgba(15, 23, 42, 0.12);
$shadow-xl: 0 24rpx 64rpx rgba(15, 23, 42, 0.16);
$shadow-2xl: 0 32rpx 80rpx rgba(15, 23, 42, 0.2);

// 渐变系统
$gradient-primary: linear-gradient(135deg, $primary-start 0%, $primary-end 100%);
$gradient-secondary: linear-gradient(135deg, $secondary 0%, #fb923c 100%);
$gradient-success: linear-gradient(135deg, $success 0%, #059669 100%);
$gradient-warm: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
$gradient-cool: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
$gradient-bg: linear-gradient(180deg, $gray-50 0%, $gray-100 100%);

// 字体系统
$font-xs: 20rpx; // 0.75rem
$font-sm: 24rpx; // 0.875rem
$font-base: 28rpx; // 1rem
$font-lg: 32rpx; // 1.125rem
$font-xl: 36rpx; // 1.25rem
$font-2xl: 48rpx; // 1.5rem
$font-3xl: 60rpx; // 1.875rem

// 行高系统
$leading-tight: 1.25;
$leading-normal: 1.5;
$leading-relaxed: 1.75;

// 🎯 组件样式

// 页面容器
.page-container {
  min-height: 100vh;
  background: $gradient-bg;
  padding: $space-4;

  &.page-container-full {
    padding: 0;
  }

  &.page-container-safe {
    padding-bottom: calc(env(safe-area-inset-bottom) + 32rpx);
  }
}

// 现代化卡片
.modern-card {
  background: $white;
  border-radius: $radius-xl;
  box-shadow: $shadow-md;
  border: 2rpx solid $gray-100;
  padding: $space-4;
  margin-bottom: $space-3;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: $shadow-lg;
  }

  // 卡片变体
  &.card-primary {
    border-color: rgba($primary-solid, 0.2);

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6rpx;
      background: $gradient-primary;
    }
  }

  &.card-elevated {
    box-shadow: $shadow-xl;
    border: none;
  }

  &.card-flat {
    box-shadow: none;
    border: 2rpx solid $gray-200;
  }

  &.card-gradient {
    background: $gradient-cool;
    border: none;
  }
}

// 现代化按钮
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $space-3 $space-4;
  border-radius: $radius-sm;
  font-size: $font-base;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  box-sizing: border-box;
  min-height: 88rpx;
  position: relative;
  overflow: hidden;

  // 按钮状态
  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }

  // 主要按钮
  &.btn-primary {
    background: $gradient-primary;
    color: $white;
    box-shadow: $shadow-sm;

    &:active {
      box-shadow: $shadow-xs;
    }
  }

  // 次要按钮
  &.btn-secondary {
    background: $white;
    color: $primary-solid;
    border: 2rpx solid $primary-solid;

    &:active {
      background: rgba($primary-solid, 0.05);
    }
  }

  // 成功按钮
  &.btn-success {
    background: $gradient-success;
    color: $white;
    box-shadow: $shadow-sm;
  }

  // 警告按钮
  &.btn-warning {
    background: $gradient-secondary;
    color: $white;
    box-shadow: $shadow-sm;
  }

  // 危险按钮
  &.btn-danger {
    background: linear-gradient(135deg, $error 0%, #dc2626 100%);
    color: $white;
    box-shadow: $shadow-sm;
  }

  // 幽灵按钮
  &.btn-ghost {
    background: transparent;
    color: $gray-600;
    border: 2rpx solid $gray-300;

    &:active {
      background: $gray-50;
    }
  }

  // 文字按钮
  &.btn-text {
    background: transparent;
    color: $primary-solid;
    box-shadow: none;

    &:active {
      background: rgba($primary-solid, 0.1);
    }
  }

  // 尺寸变体
  &.btn-sm {
    padding: $space-2 $space-3;
    font-size: $font-sm;
    min-height: 64rpx;
  }

  &.btn-lg {
    padding: $space-4 $space-6;
    font-size: $font-lg;
    min-height: 96rpx;
  }

  &.btn-full {
    width: 100%;
  }

  &.btn-round {
    border-radius: $radius-full;
  }
}

// 现代化输入框
.modern-input-group {
  margin-bottom: $space-3;

  .input-label {
    display: block;
    font-size: $font-sm;
    font-weight: 600;
    color: $gray-700;
    margin-bottom: $space-2;

    &.required::after {
      content: " *";
      color: $error;
    }
  }

  .modern-input {
    width: 100%;
    padding: $space-3 $space-4;
    border: 2rpx solid $gray-200;
    border-radius: $radius-sm;
    font-size: $font-base;
    background: $white;
    color: $gray-900;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
    min-height: 88rpx;

    &:focus {
      border-color: $primary-solid;
      outline: none;
      box-shadow: 0 0 0 6rpx rgba($primary-solid, 0.1);
    }

    &::placeholder {
      color: $gray-400;
    }

    &.input-error {
      border-color: $error;

      &:focus {
        box-shadow: 0 0 0 6rpx rgba($error, 0.1);
      }
    }

    &.input-success {
      border-color: $success;

      &:focus {
        box-shadow: 0 0 0 6rpx rgba($success, 0.1);
      }
    }
  }

  .input-help {
    font-size: $font-xs;
    color: $gray-500;
    margin-top: $space-1;
  }

  .input-error-text {
    font-size: $font-xs;
    color: $error;
    margin-top: $space-1;
  }
}

// 现代化标签
.modern-tag {
  display: inline-flex;
  align-items: center;
  padding: $space-1 $space-2;
  border-radius: $radius-xs;
  font-size: $font-xs;
  font-weight: 500;

  &.tag-primary {
    background: rgba($primary-solid, 0.1);
    color: $primary-solid;
  }

  &.tag-success {
    background: rgba($success, 0.1);
    color: $success;
  }

  &.tag-warning {
    background: rgba($warning, 0.1);
    color: $warning;
  }

  &.tag-error {
    background: rgba($error, 0.1);
    color: $error;
  }

  &.tag-gray {
    background: $gray-100;
    color: $gray-600;
  }
}

// 现代化徽章
.modern-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  height: 40rpx;
  padding: 0 $space-2;
  border-radius: $radius-full;
  font-size: $font-xs;
  font-weight: 700;
  color: $white;
  background: $error;

  &.badge-primary {
    background: $primary-solid;
  }

  &.badge-success {
    background: $success;
  }

  &.badge-warning {
    background: $warning;
  }
}

// 🎯 布局工具类

// Flexbox 布局
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-none {
  flex: none;
}

// 间距工具类
.gap-1 {
  gap: $space-1;
}
.gap-2 {
  gap: $space-2;
}
.gap-3 {
  gap: $space-3;
}
.gap-4 {
  gap: $space-4;
}
.gap-5 {
  gap: $space-5;
}
.gap-6 {
  gap: $space-6;
}

// 内边距
.p-0 {
  padding: 0;
}
.p-1 {
  padding: $space-1;
}
.p-2 {
  padding: $space-2;
}
.p-3 {
  padding: $space-3;
}
.p-4 {
  padding: $space-4;
}
.p-5 {
  padding: $space-5;
}
.p-6 {
  padding: $space-6;
}

.px-1 {
  padding-left: $space-1;
  padding-right: $space-1;
}
.px-2 {
  padding-left: $space-2;
  padding-right: $space-2;
}
.px-3 {
  padding-left: $space-3;
  padding-right: $space-3;
}
.px-4 {
  padding-left: $space-4;
  padding-right: $space-4;
}
.px-5 {
  padding-left: $space-5;
  padding-right: $space-5;
}

.py-1 {
  padding-top: $space-1;
  padding-bottom: $space-1;
}
.py-2 {
  padding-top: $space-2;
  padding-bottom: $space-2;
}
.py-3 {
  padding-top: $space-3;
  padding-bottom: $space-3;
}
.py-4 {
  padding-top: $space-4;
  padding-bottom: $space-4;
}
.py-5 {
  padding-top: $space-5;
  padding-bottom: $space-5;
}

// 外边距
.m-0 {
  margin: 0;
}
.m-1 {
  margin: $space-1;
}
.m-2 {
  margin: $space-2;
}
.m-3 {
  margin: $space-3;
}
.m-4 {
  margin: $space-4;
}
.m-5 {
  margin: $space-5;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mb-1 {
  margin-bottom: $space-1;
}
.mb-2 {
  margin-bottom: $space-2;
}
.mb-3 {
  margin-bottom: $space-3;
}
.mb-4 {
  margin-bottom: $space-4;
}
.mb-5 {
  margin-bottom: $space-5;
}
.mb-6 {
  margin-bottom: $space-6;
}

.mt-1 {
  margin-top: $space-1;
}
.mt-2 {
  margin-top: $space-2;
}
.mt-3 {
  margin-top: $space-3;
}
.mt-4 {
  margin-top: $space-4;
}
.mt-5 {
  margin-top: $space-5;
}

// 文字工具类
.text-xs {
  font-size: $font-xs;
}
.text-sm {
  font-size: $font-sm;
}
.text-base {
  font-size: $font-base;
}
.text-lg {
  font-size: $font-lg;
}
.text-xl {
  font-size: $font-xl;
}
.text-2xl {
  font-size: $font-2xl;
}
.text-3xl {
  font-size: $font-3xl;
}

.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}

.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

.leading-tight {
  line-height: $leading-tight;
}
.leading-normal {
  line-height: $leading-normal;
}
.leading-relaxed {
  line-height: $leading-relaxed;
}

// 颜色工具类
.text-white {
  color: $white;
}
.text-gray-400 {
  color: $gray-400;
}
.text-gray-500 {
  color: $gray-500;
}
.text-gray-600 {
  color: $gray-600;
}
.text-gray-700 {
  color: $gray-700;
}
.text-gray-900 {
  color: $gray-900;
}
.text-primary {
  color: $primary-solid;
}
.text-secondary {
  color: $secondary;
}
.text-success {
  color: $success;
}
.text-warning {
  color: $warning;
}
.text-error {
  color: $error;
}

// 背景工具类
.bg-white {
  background-color: $white;
}
.bg-gray-50 {
  background-color: $gray-50;
}
.bg-gray-100 {
  background-color: $gray-100;
}
.bg-gray-200 {
  background-color: $gray-200;
}
.bg-primary {
  background-color: $primary-solid;
}
.bg-secondary {
  background-color: $secondary;
}
.bg-success {
  background-color: $success;
}
.bg-warning {
  background-color: $warning;
}
.bg-error {
  background-color: $error;
}

.bg-gradient-primary {
  background: $gradient-primary;
}
.bg-gradient-secondary {
  background: $gradient-secondary;
}
.bg-gradient-success {
  background: $gradient-success;
}

// 圆角工具类
.rounded-none {
  border-radius: 0;
}
.rounded-xs {
  border-radius: $radius-xs;
}
.rounded-sm {
  border-radius: $radius-sm;
}
.rounded-md {
  border-radius: $radius-md;
}
.rounded-lg {
  border-radius: $radius-lg;
}
.rounded-xl {
  border-radius: $radius-xl;
}
.rounded-2xl {
  border-radius: $radius-2xl;
}
.rounded-full {
  border-radius: $radius-full;
}

// 阴影工具类
.shadow-none {
  box-shadow: none;
}
.shadow-xs {
  box-shadow: $shadow-xs;
}
.shadow-sm {
  box-shadow: $shadow-sm;
}
.shadow-md {
  box-shadow: $shadow-md;
}
.shadow-lg {
  box-shadow: $shadow-lg;
}
.shadow-xl {
  box-shadow: $shadow-xl;
}
.shadow-2xl {
  box-shadow: $shadow-2xl;
}

// 宽度工具类
.w-full {
  width: 100%;
}
.w-auto {
  width: auto;
}
.w-fit {
  width: fit-content;
}

// 高度工具类
.h-full {
  height: 100%;
}
.h-auto {
  height: auto;
}
.min-h-screen {
  min-height: 100vh;
}

// 显示工具类
.block {
  display: block;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.hidden {
  display: none;
}

// 位置工具类
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}

// 溢出工具类
.overflow-hidden {
  overflow: hidden;
}
.overflow-auto {
  overflow: auto;
}
.overflow-scroll {
  overflow: scroll;
}

// 动画工具类
.transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
.transition-colors {
  transition:
    color 0.2s ease,
    background-color 0.2s ease;
}
.transition-transform {
  transition: transform 0.2s ease;
}

// 交互工具类
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  user-select: none;
}

// 透明度工具类
.opacity-0 {
  opacity: 0;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-100 {
  opacity: 1;
}

// 🎯 特殊组件

// 分割线
.modern-divider {
  height: 2rpx;
  background: $gray-200;
  margin: $space-4 0;

  &.divider-dashed {
    border-top: 2rpx dashed $gray-200;
    background: none;
    height: 0;
  }

  &.divider-gradient {
    background: linear-gradient(90deg, transparent 0%, $gray-300 50%, transparent 100%);
  }
}

// 加载状态
.loading-skeleton {
  background: linear-gradient(90deg, $gray-200 25%, $gray-100 50%, $gray-200 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: $space-8 $space-4;
  color: $gray-500;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: $space-3;
    opacity: 0.5;
  }

  .empty-title {
    font-size: $font-lg;
    font-weight: 600;
    margin-bottom: $space-2;
  }

  .empty-description {
    font-size: $font-sm;
    line-height: $leading-relaxed;
  }
}
