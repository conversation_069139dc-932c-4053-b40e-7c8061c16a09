const {dishApi} = require('../../services/api');

Page({
  data: {
    currentType: 'hot',
    basketCount: 0,
    categories: [
      {type: 'hot', name: '热菜'},
      {type: 'cold', name: '凉菜'},
      {type: 'soup', name: '汤品'},
      {type: 'staple', name: '主食'},
      {type: 'dessert', name: '甜品'}
    ],
    loading: true,
    foodData: {},
    foodList: []
  },

  async onLoad() {
    // 加载菜品数据
    await this.loadDishes();
    // 初始化默认分类的菜品列表
    this.renderFoodList('hot');
    // 更新购物篮数量
    this.updateBasketCount();
  },

  // 加载菜品数据
  async loadDishes() {
    try {
      wx.showLoading({title: '加载中...'});

      const result = await dishApi.getDishesByCategory();

      if (result.code === 200) {
        this.setData({
          foodData: result.data,
          loading: false
        });
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('加载菜品失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  onShow() {
    // 页面显示时更新购物篮数量
    this.updateBasketCount();
  },

  // 切换分类
  switchCategory(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      currentType: type
    });
    this.renderFoodList(type);
  },

  // 渲染菜品列表
  renderFoodList(type) {
    const foodList = this.data.foodData[type] || [];

    // 为每个菜品添加 isAdding 属性，用于控制点击动画
    const enhancedFoodList = foodList.map(item => ({
      ...item,
      isAdding: false
    }));

    this.setData({
      foodList: enhancedFoodList
    });
  },

  // 跳转到菜品详情页
  async goToDetail(e) {
    const id = e.currentTarget.dataset.id;

    try {
      wx.showLoading({title: '加载中...'});

      const result = await dishApi.getDishDetail(id);

      if (result.code === 200) {
        // 将详情数据存入缓存
        wx.setStorageSync('detailData', result.data);

        // 跳转到详情页
        wx.navigateTo({
          url: '/pages/detail/index'
        });
      } else {
        wx.showToast({
          title: '获取详情失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('获取菜品详情失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 添加到购物篮
  addToBasket(e) {
    const {id, name, remark, img, index} = e.currentTarget.dataset;

    // 获取购物篮数据
    let basket = wx.getStorageSync('basket') || {};

    if (!basket[id]) {
      basket[id] = {id, name, remark, img, count: 1};
    } else {
      basket[id].count += 1;
    }

    // 保存购物篮数据
    wx.setStorageSync('basket', basket);

    // 更新购物篮数量
    this.updateBasketCount();

    // 显示添加动画
    const foodList = [...this.data.foodList];
    foodList[index].isAdding = true;
    this.setData({foodList});

    // 动画结束后恢复
    setTimeout(() => {
      foodList[index].isAdding = false;
      this.setData({foodList});
    }, 600);

    // 提示用户
    wx.showToast({
      title: '已添加到购物篮',
      icon: 'success',
      duration: 1000
    });
  },

  // 更新购物篮数量
  updateBasketCount() {
    const basket = wx.getStorageSync('basket') || {};
    const total = Object.values(basket).reduce(
      (sum, item) => sum + item.count,
      0
    );

    this.setData({
      basketCount: total
    });
  },

  // 跳转到购物篮页面
  goToBasket() {
    wx.navigateTo({
      url: '/pages/today_order/index'
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
