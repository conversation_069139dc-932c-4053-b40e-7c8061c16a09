/* 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
}

.page-header {
  @include flex;
  @include items-center;
  @include justify-center;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #00f2ea;
  text-align: center;
}

.history-section {
  max-width: 840rpx;
  margin: 20rpx auto 0 auto;
  background: #f3f4f6;
  @include rounded-lg;
  box-shadow: 0 8rpx 48rpx rgba(0, 242, 234, 0.13);
  padding: 30rpx 24rpx 24rpx 24rpx;
  position: relative;
}

.icon-margin {
  margin-right: 10rpx;
}

.history-list {
  @include flex;
  @include flex-col;
  gap: 22rpx;
}

.history-card {
  background: linear-gradient(120deg, #23272f 60%, #23233b 100%);
  border-radius: 22rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 242, 234, 0.13);
  padding: 20rpx 24rpx;
  @include flex;
  @include flex-col;
  gap: 10rpx;
  position: relative;

  &.today-card {
    background: linear-gradient(120deg, #2a2a3a 60%, #2a2a4a 100%);
    box-shadow: 0 4rpx 24rpx rgba(254, 44, 85, 0.2);
    border: 2rpx solid rgba(254, 44, 85, 0.3);
  }
}

.history-date {
  color: #00f2ea;
  font-weight: bold;
  font-size: 32rpx;
  margin-bottom: 4rpx;
  @include flex;
  @include items-center;
  position: relative;
}

.today-tag {
  position: absolute;
  right: 0;
  top: 0;
  background: #fe2c55;
  color: #111827;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-weight: normal;
}

.history-summary {
  color: #4b5563;
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.history-remark {
  color: #ffd580;
  font-size: 28rpx;
  margin-top: 2rpx;
  word-break: break-all;
  @include flex;
  @include items-center;
}

/* 菜品清单样式 */
.history-dishes {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid rgba(0, 242, 234, 0.2);
}

.dishes-title {
  font-size: 26rpx;
  color: #4b5563;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.dishes-list {
  @include flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.dish-item {
  background: rgba(0, 242, 234, 0.1);
  color: #00f2ea;
  padding: 8rpx 16rpx;
  @include rounded-md;
  font-size: 24rpx;
  border: 1rpx solid rgba(0, 242, 234, 0.3);
  white-space: nowrap;
}

.back-btn {
  @include flex;
  @include items-center;
  @include justify-center;
  margin: 40rpx auto 0 auto;
  @include modern-btn; @include btn-primary;
  color: #111827;
  font-weight: bold;
  padding: 10rpx 44rpx;
  @include rounded-lg;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  position: fixed;
  right: 44rpx;
  bottom: 44rpx;
  z-index: 100;
  min-width: 220rpx;
}
