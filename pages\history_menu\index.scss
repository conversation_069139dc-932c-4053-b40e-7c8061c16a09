.container {
  background: #f9fafb;
  color: #111827;
  min-height: 100vh;
  padding: 20rpx;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #00f2ea;
  text-align: center;
}

.history-section {
  max-width: 840rpx;
  margin: 20rpx auto 0 auto;
  background: #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 48rpx rgba(0, 242, 234, 0.13);
  padding: 30rpx 24rpx 24rpx 24rpx;
  position: relative;
}

.icon-margin {
  margin-right: 10rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 22rpx;
}

.history-card {
  background: linear-gradient(120deg, #23272f 60%, #23233b 100%);
  border-radius: 22rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 242, 234, 0.13);
  padding: 20rpx 24rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  position: relative;

  &.today-card {
    background: linear-gradient(120deg, #2a2a3a 60%, #2a2a4a 100%);
    box-shadow: 0 4rpx 24rpx rgba(254, 44, 85, 0.2);
    border: 2rpx solid rgba(254, 44, 85, 0.3);
  }
}

.history-date {
  color: #00f2ea;
  font-weight: bold;
  font-size: 32rpx;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.today-tag {
  position: absolute;
  right: 0;
  top: 0;
  background: #fe2c55;
  color: #111827;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-weight: normal;
}

.history-summary {
  color: #4b5563;
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.history-remark {
  color: #ffd580;
  font-size: 28rpx;
  margin-top: 2rpx;
  word-break: break-all;
  display: flex;
  align-items: center;
}

/* 菜品清单样式 */
.history-dishes {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid rgba(0, 242, 234, 0.2);
}

.dishes-title {
  font-size: 26rpx;
  color: #4b5563;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.dishes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.dish-item {
  background: rgba(0, 242, 234, 0.1);
  color: #00f2ea;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: 1rpx solid rgba(0, 242, 234, 0.3);
  white-space: nowrap;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40rpx auto 0 auto;
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #111827;
  font-weight: bold;
  padding: 10rpx 44rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  position: fixed;
  right: 44rpx;
  bottom: 44rpx;
  z-index: 100;
  min-width: 220rpx;
}
