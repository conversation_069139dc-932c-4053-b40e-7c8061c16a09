const express = require('express');
const router = express.Router();
const messageController = require('../controllers/messageController');
const {auth, adminAuth} = require('../middlewares/auth');

// 获取消息列表
router.get('/', messageController.getMessages);

// 获取留言统计信息 (需要管理员权限)
router.get(
  '/statistics',
  auth,
  adminAuth,
  messageController.getMessageStatistics
);

// 获取指定消息
router.get('/:id', messageController.getMessageById);

// 创建消息 (需要认证)
router.post('/', auth, messageController.createMessage);

// 更新消息 (需要认证)
router.put('/:id', auth, messageController.updateMessage);

// 删除消息 (需要认证)
router.delete('/:id', auth, messageController.deleteMessage);

// 批量标记为已读 (需要管理员权限)
router.post('/batch-read', auth, adminAuth, messageController.batchMarkAsRead);

// 批量删除留言 (需要管理员权限)
router.post(
  '/batch-delete',
  auth,
  adminAuth,
  messageController.batchDeleteMessages
);

module.exports = router;
