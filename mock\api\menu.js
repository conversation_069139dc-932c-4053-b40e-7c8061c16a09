/**
 * 菜单相关 API
 */

const { mockCategories, mockDishes, mockTodayMenu, mockHistoryMenus } = require('../menu');

// 菜单数据
let categories = [...mockCategories];
let dishes = [...mockDishes];
let todayMenu = { ...mockTodayMenu };
let historyMenus = [...mockHistoryMenus];

// 处理菜单相关请求
function handleRequest(req) {
  const { method, params, data } = req;
  const { id, subResource } = params;
  
  // 根据请求方法和路径处理不同的请求
  switch (method) {
    case 'GET':
      if (subResource === 'categories') {
        // GET /menus/categories - 获取所有菜品类别
        return getAllCategories();
      } else if (subResource === 'dishes') {
        // GET /menus/dishes - 获取所有菜品
        return getAllDishes();
      } else if (subResource === 'today') {
        // GET /menus/today - 获取今日菜单
        return getTodayMenu();
      } else if (subResource === 'history') {
        // GET /menus/history - 获取历史菜单
        return getHistoryMenus();
      } else if (id) {
        // GET /menus/:id - 获取指定菜单
        return getMenuById(id);
      } else {
        // GET /menus - 获取推荐菜单
        return getRecommendedMenu();
      }
    
    case 'POST':
      if (subResource === 'dishes') {
        // POST /menus/dishes - 创建新菜品
        return createDish(data);
      } else {
        // POST /menus - 创建新菜单
        return createMenu(data);
      }
      
    case 'PUT':
      if (id && subResource === 'dishes') {
        // PUT /menus/:id/dishes - 更新菜单中的菜品
        return updateMenuDishes(id, data);
      } else if (id) {
        // PUT /menus/:id - 更新菜单
        return updateMenu(id, data);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    case 'DELETE':
      if (id) {
        // DELETE /menus/:id - 删除菜单
        return deleteMenu(id);
      }
      return { code: 400, message: 'Invalid request', data: null };
      
    default:
      return { code: 405, message: 'Method not allowed', data: null };
  }
}

// 获取所有菜品类别
function getAllCategories() {
  return {
    code: 200,
    message: 'Success',
    data: categories
  };
}

// 获取所有菜品
function getAllDishes() {
  return {
    code: 200,
    message: 'Success',
    data: dishes
  };
}

// 获取今日菜单
function getTodayMenu() {
  return {
    code: 200,
    message: 'Success',
    data: todayMenu
  };
}

// 获取历史菜单
function getHistoryMenus() {
  return {
    code: 200,
    message: 'Success',
    data: historyMenus
  };
}

// 获取指定菜单
function getMenuById(id) {
  // 如果是今日菜单
  if (id === 'today') {
    return getTodayMenu();
  }
  
  // 查找历史菜单
  const menu = historyMenus.find(m => m.date === id);
  
  if (menu) {
    return {
      code: 200,
      message: 'Success',
      data: menu
    };
  }
  
  return {
    code: 404,
    message: 'Menu not found',
    data: null
  };
}

// 获取推荐菜单
function getRecommendedMenu() {
  // 简单地返回前3个菜品作为推荐
  const recommended = dishes.slice(0, 3).map(dish => ({
    id: dish.id,
    name: dish.name,
    image: dish.image,
    count: dish.count
  }));
  
  return {
    code: 200,
    message: 'Success',
    data: recommended
  };
}

// 创建新菜品
function createDish(data) {
  if (!data || !data.name || !data.category_id) {
    return {
      code: 400,
      message: 'Invalid dish data',
      data: null
    };
  }
  
  const newDish = {
    id: dishes.length + 1,
    name: data.name,
    category_id: data.category_id,
    image: data.image || '',
    description: data.description || '',
    count: 0,
    created_at: new Date().toISOString().split('T')[0]
  };
  
  dishes.push(newDish);
  
  return {
    code: 201,
    message: 'Dish created',
    data: newDish
  };
}

// 创建新菜单
function createMenu(data) {
  if (!data || !data.date || !data.dishes || !Array.isArray(data.dishes)) {
    return {
      code: 400,
      message: 'Invalid menu data',
      data: null
    };
  }
  
  // 如果是今日菜单
  if (data.date === todayMenu.date) {
    todayMenu = { ...data };
    return {
      code: 201,
      message: 'Today menu updated',
      data: todayMenu
    };
  }
  
  // 添加到历史菜单
  const existingIndex = historyMenus.findIndex(m => m.date === data.date);
  
  if (existingIndex !== -1) {
    historyMenus[existingIndex] = { ...data };
  } else {
    historyMenus.unshift({ ...data });
  }
  
  return {
    code: 201,
    message: 'Menu created',
    data: data
  };
}

// 更新菜单
function updateMenu(id, data) {
  if (id === 'today') {
    todayMenu = { ...todayMenu, ...data };
    return {
      code: 200,
      message: 'Today menu updated',
      data: todayMenu
    };
  }
  
  const index = historyMenus.findIndex(m => m.date === id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Menu not found',
      data: null
    };
  }
  
  historyMenus[index] = { ...historyMenus[index], ...data };
  
  return {
    code: 200,
    message: 'Menu updated',
    data: historyMenus[index]
  };
}

// 更新菜单中的菜品
function updateMenuDishes(id, data) {
  if (!data || !Array.isArray(data)) {
    return {
      code: 400,
      message: 'Invalid dishes data',
      data: null
    };
  }
  
  if (id === 'today') {
    todayMenu.dishes = [...data];
    return {
      code: 200,
      message: 'Today menu dishes updated',
      data: todayMenu
    };
  }
  
  const index = historyMenus.findIndex(m => m.date === id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Menu not found',
      data: null
    };
  }
  
  historyMenus[index].dishes = [...data];
  
  return {
    code: 200,
    message: 'Menu dishes updated',
    data: historyMenus[index]
  };
}

// 删除菜单
function deleteMenu(id) {
  const index = historyMenus.findIndex(m => m.date === id);
  
  if (index === -1) {
    return {
      code: 404,
      message: 'Menu not found',
      data: null
    };
  }
  
  const deletedMenu = historyMenus[index];
  historyMenus = historyMenus.filter(m => m.date !== id);
  
  return {
    code: 200,
    message: 'Menu deleted',
    data: deletedMenu
  };
}

module.exports = {
  handleRequest
};
