<template>
  <div class="statistics-overview">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in statistics" :key="index">
        <div :class="['stats-card', stat.type]">
          <div class="stats-title">{{ stat.title }}</div>
          <div class="stats-value">{{ stat.value }}</div>
          <div class="stats-desc">{{ stat.desc }}</div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>订单趋势分析</h3>
          <div ref="orderTrendRef" style="height: 300px;"></div>
        </div>
      </el-col>
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>菜品热度排行</h3>
          <div ref="dishRankRef" style="height: 300px;"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 数据表格 -->
    <el-row :gutter="20">
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>热门菜品统计</h3>
          <el-table :data="popularDishes" style="width: 100%">
            <el-table-column prop="name" label="菜品名称" />
            <el-table-column prop="orderCount" label="点单次数" />
            <el-table-column prop="category" label="分类" />
          </el-table>
        </div>
      </el-col>
      <el-col :xs="24" :md="12">
        <div class="content-card">
          <h3>用户活跃度</h3>
          <el-table :data="activeUsers" style="width: 100%">
            <el-table-column prop="name" label="用户名" />
            <el-table-column prop="orderCount" label="订单数" />
            <el-table-column prop="lastOrder" label="最后下单" />
          </el-table>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  name: 'StatisticsOverview',
  setup() {
    const orderTrendRef = ref()
    const dishRankRef = ref()
    
    const statistics = ref([
      { title: '总订单数', value: 156, desc: '本月新增 23', type: 'primary' },
      { title: '活跃用户', value: 8, desc: '本周活跃 6', type: 'success' },
      { title: '热门菜品', value: 12, desc: '本月最受欢迎', type: 'warning' },
      { title: '平均评分', value: 4.8, desc: '用户满意度', type: 'danger' }
    ])
    
    const popularDishes = ref([
      { name: '红烧肉', orderCount: 45, category: '热菜' },
      { name: '西红柿鸡蛋', orderCount: 38, category: '热菜' },
      { name: '凉拌黄瓜', orderCount: 32, category: '凉菜' },
      { name: '紫菜蛋花汤', orderCount: 28, category: '汤品' },
      { name: '米饭', orderCount: 56, category: '主食' }
    ])
    
    const activeUsers = ref([
      { name: '楠楠', orderCount: 23, lastOrder: '2024-05-28' },
      { name: '爸爸', orderCount: 18, lastOrder: '2024-05-27' },
      { name: '妈妈', orderCount: 15, lastOrder: '2024-05-28' },
      { name: '小明', orderCount: 12, lastOrder: '2024-05-26' }
    ])
    
    // 初始化订单趋势图表
    const initOrderTrendChart = () => {
      const chart = echarts.init(orderTrendRef.value)
      const option = {
        title: {
          text: '近30天订单趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['订单数', '完成数']
        },
        xAxis: {
          type: 'category',
          data: ['5-1', '5-5', '5-10', '5-15', '5-20', '5-25', '5-28']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '订单数',
            type: 'line',
            data: [8, 12, 15, 18, 22, 25, 28],
            itemStyle: { color: '#1890ff' }
          },
          {
            name: '完成数',
            type: 'line',
            data: [7, 11, 14, 17, 21, 24, 26],
            itemStyle: { color: '#52c41a' }
          }
        ]
      }
      chart.setOption(option)
    }
    
    // 初始化菜品排行图表
    const initDishRankChart = () => {
      const chart = echarts.init(dishRankRef.value)
      const option = {
        title: {
          text: '菜品点单排行'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: ['米饭', '红烧肉', '西红柿鸡蛋', '凉拌黄瓜', '紫菜蛋花汤']
        },
        series: [{
          type: 'bar',
          data: [56, 45, 38, 32, 28],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      }
      chart.setOption(option)
    }
    
    onMounted(() => {
      nextTick(() => {
        initOrderTrendChart()
        initDishRankChart()
      })
    })
    
    return {
      orderTrendRef,
      dishRankRef,
      statistics,
      popularDishes,
      activeUsers
    }
  }
})
</script>
