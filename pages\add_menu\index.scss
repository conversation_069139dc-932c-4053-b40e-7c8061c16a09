/* 添加菜品页面 - Tailwind CSS 风格 */

.container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding: 32rpx 24rpx;
}

.main-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  border-radius: 24rpx;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  margin-bottom: 32rpx;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #ec4899, #3b82f6);
    border-radius: 24rpx 24rpx 0 0;
  }
}

/* 标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ec4899;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 表单样式 */
.input-container {
  margin-bottom: 32rpx;
  width: 100%;
}

.input-warm {
  background-color: #111827;
  color: #111827;
  border: 2rpx solid #ec4899;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  transition: all 0.2s ease;
}

.input-warm:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

.textarea-style {
  min-height: 120rpx;
  line-height: 1.5;
  padding: 20rpx 24rpx;
}

.placeholder-style {
  color: #9ca3af;
  font-size: 26rpx;
}

.upload-img-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 36rpx;
}

.upload-img-preview {
  width: 144rpx;
  height: 144rpx;
  border-radius: 24rpx;
  object-fit: cover;
  background: #f3f4f6;
  border: 3rpx solid #00f2ea;
  box-shadow: 0 4rpx 16rpx rgba(0, 242, 234, 0.2);
}

.upload-img-label {
  display: inline-block;
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #111827;
  font-weight: bold;
  border-radius: 16rpx;
  padding: 20rpx 48rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
}

.picker-label {
  font-size: 28rpx;
  color: #00f2ea;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.picker-warm {
  background: #f3f4f6;
  border: 2rpx solid #fe2c55;
  border-radius: 24rpx;
  padding: 0 32rpx;
  height: 88rpx;
  line-height: 88rpx;
}

.picker-text {
  color: #111827;
  font-size: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow-icon {
  color: #999;
  font-size: 24rpx !important;
}

.submit-btn {
  background: linear-gradient(90deg, #fe2c55 60%, #00f2ea 100%);
  color: #111827;
  border: none;
  border-radius: 28rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  text-align: center;
}
