/* 添加菜品页面 - Tailwind CSS 风格 */

.container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding: 32rpx 24rpx;
}

.main-card {
  background: white;
  border-radius: 16rpx;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin-bottom: 32rpx;
  padding: 32rpx;
  border: 2rpx solid #e5e7eb;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #3b82f6, #ec4899);
    border-radius: 16rpx 16rpx 0 0;
  }
}

/* 标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 表单样式 */
.input-container {
  margin-bottom: 32rpx;
  width: 100%;
}

.input-warm {
  background-color: white;
  color: #111827;
  border: 2rpx solid #3b82f6;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  transition: all 0.2s ease;
  box-sizing: border-box; /* 修复宽度超出问题 */
}

.input-warm:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

.textarea-style {
  min-height: 120rpx;
  line-height: 1.5;
  padding: 20rpx 24rpx;
}

.placeholder-style {
  color: #9ca3af;
  font-size: 26rpx;
}

.upload-img-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 36rpx;
}

.upload-img-preview {
  width: 144rpx;
  height: 144rpx;
  border-radius: 16rpx;
  object-fit: cover;
  background: #f3f4f6;
  border: 2rpx solid #3b82f6;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.upload-img-label {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #ec4899);
  color: white;
  font-weight: 600;
  border-radius: 12rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.picker-label {
  font-size: 28rpx;
  color: #3b82f6;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.picker-warm {
  background: white;
  border: 2rpx solid #3b82f6;
  border-radius: 12rpx;
  padding: 0 24rpx;
  height: 80rpx;
  line-height: 80rpx;
  box-sizing: border-box;
}

.picker-text {
  color: #111827;
  font-size: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow-icon {
  color: #999;
  font-size: 24rpx !important;
}

.submit-btn {
  background: linear-gradient(135deg, #3b82f6, #ec4899);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 24rpx;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  text-align: center;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:active {
    transform: scale(0.98);
  }
}
