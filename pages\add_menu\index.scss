/* 现代化设计 */
@import "../../styles/modern-design.scss";

/* 添加菜品页面 - Tailwind CSS 风格 */

.container {
  @extend .page-container;
  @extend .page-container-safe;
}

.main-card {
  @extend .modern-card;
  @extend .rounded-md;
  @extend .shadow-md;
  @extend .mb-4;
  @extend .p-4;
  border: 2rpx solid #e5e7eb;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    @extend .modern-btn; @extend .btn-primary;
    @extend .rounded-md 16rpx 0 0;
  }
}

/* 标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #3b82f6;
  @extend .mb-4;
  @extend .flex;
  @extend .items-center;
  gap: 12rpx;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 表单样式 */
.input-container {
  @extend .mb-4;
  width: 100%;
}

.input-warm {
  background-@extend .text-gray-900;
  color: #111827;
  border: 2rpx solid #3b82f6;
  @extend .rounded-sm;
  padding: 0 24rpx;
  font-size: 28rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  transition: all 0.2s ease;
  box-sizing: border-box; /* 修复宽度超出问题 */
}

.input-warm:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

.textarea-style {
  min-height: 120rpx;
  line-height: 1.5;
  padding: 20rpx 24rpx;
}

.placeholder-style {
  color: #9ca3af;
  font-size: 26rpx;
}

.upload-img-box {
  @extend .flex;
  @extend .items-center;
  gap: 20rpx;
  margin-bottom: 36rpx;
}

.upload-img-preview {
  width: 144rpx;
  height: 144rpx;
  @extend .rounded-md;
  object-fit: cover;
  background: #f3f4f6;
  border: 2rpx solid #3b82f6;
  @extend .shadow-md;
}

.upload-img-label {
  display: inline-block;
  @extend .modern-btn; @extend .btn-primary;
  @extend .text-gray-900;
  font-weight: 600;
  @extend .rounded-sm;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  @extend .shadow-md;
}

.picker-label {
  font-size: 28rpx;
  color: #3b82f6;
  @extend .mb-2;
  @extend .flex;
  @extend .items-center;
}

.picker-warm {
  @extend .modern-card;
  border: 2rpx solid #3b82f6;
  @extend .rounded-sm;
  padding: 0 24rpx;
  height: 80rpx;
  line-height: 80rpx;
  box-sizing: border-box;
}

.picker-text {
  color: #111827;
  font-size: 30rpx;
  @extend .flex;
  @extend .justify-between;
  @extend .items-center;
}

.arrow-icon {
  color: #999;
  font-size: 24rpx !important;
}

.submit-btn {
  @extend .modern-btn; @extend .btn-primary;
  @extend .text-gray-900;
  border: none;
  @extend .rounded-sm;
  padding: 20rpx 0;
  font-size: 28rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 24rpx;
  @extend .shadow-md;
  text-align: center;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:active {
    transform: scale(0.98);
  }
}


/* 添加菜单页面特定样式 */
.main-card {
  @extend .modern-card;
  @extend .card-primary;
  @extend .p-4;
  @extend .mb-4;
}

.section-title {
  @extend .text-lg;
  @extend .font-semibold;
  @extend .text-primary;
  @extend .mb-3;
}

.input-warm {
  @extend .modern-input;
}

.submit-btn {
  @extend .modern-btn;
  @extend .btn-gradient;
  @extend .btn-full;
}