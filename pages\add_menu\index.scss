/* 添加菜品页面 - 小程序兼容设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;

  /* 隐藏滚动条 */
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.main-card {
  @include modern-card;
  @include card-primary;
  width: 100%;
}

/* 标题样式 */
.section-title {
  @include text-lg;
  @include font-semibold;
  @include text-primary;
  @include mb-4;
  @include flex;
  @include items-center;
  @include gap-3;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 表单样式 */
.input-container {
  @include mb-4;
  width: 100%;
}

.input-warm {
  @include modern-input;
  color: $gray-900;
  border: 2rpx solid $primary-solid;

  &:focus {
    border-color: $primary-solid;
    box-shadow: 0 0 0 4rpx rgba($primary-solid, 0.1);
  }
}

.textarea-style {
  min-height: 120rpx;
  line-height: 1.5;
  padding: 20rpx 24rpx;
}

.placeholder-style {
  color: $gray-400;
  @include text-sm;
}

/* 图片上传样式 */
.upload-img-box {
  @include flex;
  @include items-center;
  @include gap-3;
  @include mb-4;
}

.upload-img-preview {
  width: 144rpx;
  height: 144rpx;
  @include rounded-md;
  object-fit: cover;
  background: $gray-100;
  border: 2rpx solid $primary-solid;
  @include shadow-sm;
}

.upload-img-label {
  @include modern-btn;
  @include btn-primary;
  @include text-white;
  @include font-semibold;
  @include rounded-sm;
  padding: 20rpx 32rpx;
  @include text-base;
  @include shadow-sm;
}

/* 选择器样式 */
.picker-label {
  @include text-base;
  @include text-primary;
  @include mb-2;
  @include flex;
  @include items-center;
}

.picker-warm {
  @include modern-card;
  border: 2rpx solid $primary-solid;
  @include rounded-sm;
  padding: 0 24rpx;
  height: 80rpx;
  line-height: 80rpx;
  box-sizing: border-box;
}

.picker-text {
  color: $gray-900;
  @include text-lg;
  @include flex;
  @include justify-between;
  @include items-center;
}

.arrow-icon {
  color: $gray-400;
  font-size: 24rpx !important;
}

/* 提交按钮 */
.submit-btn {
  @include modern-btn;
  @include btn-primary;
  @include text-white;
  border: none;
  @include rounded-sm;
  padding: 20rpx 0;
  @include text-base;
  @include font-semibold;
  width: 100%;
  margin-top: 24rpx;
  @include shadow-sm;
  @include text-center;
  @include transition;
  box-sizing: border-box;

  &:active {
    transform: scale(0.98);
  }
}
