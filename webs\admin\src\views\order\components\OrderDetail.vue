<template>
  <div class="order-detail">
    <div class="detail-header">
      <div class="order-info">
        <h3>订单详情</h3>
        <div class="order-meta">
          <span class="order-id">订单号：{{ order.id }}</span>
          <el-tag :type="getStatusType(order.status)" size="large">
            {{ getStatusText(order.status) }}
          </el-tag>
        </div>
      </div>
    </div>
    
    <el-divider />
    
    <div class="detail-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-section">
            <h4>用户信息</h4>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="姓名">
                {{ order.user?.name || '未知用户' }}
              </el-descriptions-item>
              <el-descriptions-item label="手机号">
                {{ order.user?.phone || '未提供' }}
              </el-descriptions-item>
              <el-descriptions-item label="微信昵称">
                {{ order.user?.nickname || '未提供' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-section">
            <h4>订单信息</h4>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="用餐时间">
                {{ formatTime(order.mealTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="下单时间">
                {{ formatTime(order.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatTime(order.updatedAt) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-col>
      </el-row>
      
      <div class="info-section">
        <h4>菜品清单</h4>
        <el-table :data="orderItems" border size="small">
          <el-table-column prop="dishName" label="菜品名称" />
          <el-table-column prop="count" label="数量" width="80" align="center" />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.remark || '无' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="info-section" v-if="order.remark">
        <h4>订单备注</h4>
        <div class="remark-content">
          {{ order.remark }}
        </div>
      </div>
      
      <div class="info-section">
        <h4>状态操作</h4>
        <div class="status-actions">
          <el-button
            v-if="order.status === 'pending'"
            type="success"
            @click="handleStatusChange('completed')"
            :loading="statusLoading"
          >
            标记为已完成
          </el-button>
          <el-button
            v-if="order.status === 'pending'"
            type="danger"
            @click="handleStatusChange('cancelled')"
            :loading="statusLoading"
          >
            取消订单
          </el-button>
          <el-button
            v-if="order.status === 'cancelled'"
            type="primary"
            @click="handleStatusChange('pending')"
            :loading="statusLoading"
          >
            恢复订单
          </el-button>
          <el-button
            v-if="order.status === 'completed'"
            type="warning"
            @click="handleStatusChange('pending')"
            :loading="statusLoading"
          >
            重新处理
          </el-button>
        </div>
      </div>
    </div>
    
    <div class="detail-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handlePrint">
        <el-icon><Printer /></el-icon>
        打印订单
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Printer } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import { formatTime } from '@/utils/common'

const props = defineProps({
  order: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'statusChanged'])

const statusLoading = ref(false)

// 计算订单项目
const orderItems = computed(() => {
  try {
    return JSON.parse(props.order.items || '[]')
  } catch {
    return []
  }
})

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

// 状态变化处理
const handleStatusChange = async (newStatus) => {
  try {
    const statusText = getStatusText(newStatus)
    await ElMessageBox.confirm(
      `确定要将订单状态改为"${statusText}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    statusLoading.value = true
    await orderApi.updateOrderStatus(props.order.id, newStatus)
    
    ElMessage.success('状态更新成功')
    emit('statusChanged', newStatus)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新订单状态失败:', error)
      ElMessage.error('状态更新失败')
    }
  } finally {
    statusLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
}

// 打印订单
const handlePrint = () => {
  // 创建打印内容
  const printContent = `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="text-align: center; margin-bottom: 20px;">订单详情</h2>
      <div style="margin-bottom: 15px;">
        <strong>订单号：</strong>${props.order.id}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>用户：</strong>${props.order.user?.name || '未知用户'}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>手机号：</strong>${props.order.user?.phone || '未提供'}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>用餐时间：</strong>${formatTime(props.order.mealTime)}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>下单时间：</strong>${formatTime(props.order.createdAt)}
      </div>
      <div style="margin-bottom: 20px;">
        <strong>状态：</strong>${getStatusText(props.order.status)}
      </div>
      
      <h3>菜品清单：</h3>
      <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">菜品名称</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">数量</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">备注</th>
          </tr>
        </thead>
        <tbody>
          ${orderItems.value.map(item => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${item.dishName}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${item.count}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${item.remark || '无'}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      ${props.order.remark ? `
        <div style="margin-bottom: 20px;">
          <strong>订单备注：</strong>${props.order.remark}
        </div>
      ` : ''}
      
      <div style="text-align: center; margin-top: 30px; color: #666;">
        打印时间：${formatTime(new Date())}
      </div>
    </div>
  `
  
  // 创建新窗口并打印
  const printWindow = window.open('', '_blank')
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>订单详情 - ${props.order.id}</title>
      <style>
        @media print {
          body { margin: 0; }
          @page { margin: 1cm; }
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `)
  printWindow.document.close()
  printWindow.print()
  printWindow.close()
  
  ElMessage.success('打印任务已发送')
}
</script>

<style scoped lang="scss">
.order-detail {
  .detail-header {
    margin-bottom: 20px;
    
    .order-info {
      h3 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 18px;
      }
      
      .order-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .order-id {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }
  
  .detail-content {
    .info-section {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
        font-weight: 500;
        border-left: 4px solid #409eff;
        padding-left: 12px;
      }
      
      .remark-content {
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #409eff;
        color: #333;
        line-height: 1.6;
      }
      
      .status-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
      }
    }
  }
  
  .detail-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .order-detail {
    .detail-header {
      .order-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
    
    .detail-content {
      .info-section {
        .status-actions {
          flex-direction: column;
          
          .el-button {
            width: 100%;
          }
        }
      }
    }
    
    .detail-footer {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    background-color: #f8fafc;
  }
  
  .el-table__row:hover {
    background-color: #f1f5f9;
  }
}

// 描述列表样式优化
:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
    color: #333;
    width: 80px;
  }
  
  .el-descriptions__content {
    color: #666;
  }
}
</style>
