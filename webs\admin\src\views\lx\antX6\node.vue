<template>
  <div class="item">
    <div>{{ label }}</div>
    <div>1000元</div>
  </div>
</template>

<script setup>
import { inject } from "vue";

// 使用 inject 从 x6 传递的上下文中提取 data
const getNode = inject("getNode");
const { label } = getNode().data;
</script>

<style lang="scss" scoped>
.item {
  width: 120px;
  height: 68px;
  border: 1px solid #f2f3f5;
  background: white;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
}
</style>
