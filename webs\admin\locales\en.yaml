buttons:
  hsLoginOut: LoginOut
  hsfullscreen: FullScreen
  hsexitfullscreen: ExitFullscreen
  hsrefreshRoute: RefreshRoute
  hslogin: Login
  hsadd: Add
  hsmark: Mark/Cancel
  hssave: Save
  hssearch: Search
  hsexpendAll: Expand All
  hscollapseAll: Collapse All
  hssystemSet: Open ProjectConfig
  hsdelete: Delete
  hsreload: Reload
  hscloseCurrentTab: Close CurrentTab
  hscloseLeftTabs: Close LeftTabs
  hscloseRightTabs: Close RightTabs
  hscloseOtherTabs: Close OtherTabs
  hscloseAllTabs: Close AllTabs
  hswholeFullScreen: FullScreen
  hswholeExitFullScreen: ExitFull
  hscontentFullScreen: Content FullScreen
  hscontentExitFullScreen: Content ExitFullScreen
menus:
  hshome: Home
  hslogin: Login
  hsempty: Empty Page
  hssysManagement: System Manage
  hsUser: User Manage
  hsRole: Role Manage
  hsDept: Dept Manage
  hseditor: Editor
  hsabnormal: Abnormal Page
  hsfourZeroFour: "404"
  hsfourZeroOne: "403"
  hsFive: "500"
  hscomponents: Components
  hsdialog: Dialog Components
  hsmessage: Message Tips Components
  hsvideo: Video Components
  hssegmented: Segmented Components
  hswaterfall: Waterfall Components
  hsmap: Map Components
  hsdraggable: Draggable Components
  hssplitPane: Split Pane
  hsbutton: Button Components
  hscropping: Picture Cropping
  hsControl: segmentation control
  pureText: Pure Text
  hsanimatecss: AnimateCss Selector
  hscountTo: Digital Animation
  hsselector: Selector Components
  hsflowChart: Flow Chart
  hsseamless: Seamless Scroll
  hscontextmenu: Context Menu
  hstypeit: Typeit Components
  hsjsoneditor: JSON Editor
  hsmenus: MultiLevel Menu
  hsmenu1: Menu1
  hsmenu1-1: Menu1-1
  hsmenu1-2: Menu1-2
  hsmenu1-2-1: Menu1-2-1
  hsmenu1-2-2: Menu1-2-2
  hsmenu1-3: Menu1-3
  hsmenu2: Menu2
  permission: Permission Manage
  permissionPage: Page Permission
  permissionButton: Button Permission
  hstabs: Tabs Operate
  hsguide: Guide
  hsAble: Able
  hsMenuTree: Menu Tree
  hsVideoFrame: Video Frame Capture
  hsWavesurfer: Audio Visualization
  hsOptimize: Debounce、Throttle、Copy、Longpress Directives
  hsWatermark: Water Mark
  hsPrint: Print
  hsDownload: Download
  hsExternalPage: External Page
  hsPureDocument: Pure Doc(Embedded)
  externalLink: zhufeng Doc(External)
  hsEpDocument: Element Plus Doc(Embedded)
  hsVueDocument: Vue3 Doc(Embedded)
  hsViteDocument: Vite Doc(Embedded)
  hsPiniaDocument: Pinia Doc(Embedded)
  hsRouterDocument: Vue Router Doc(Embedded)
  hsTailwindcssDocument: Tailwindcss Doc(Embedded)
  hsAbout: About
  hsResult: Result Page
  hsSuccess: Success Page
  hsFail: Fail Page
  hsIconSelect: Icon Select
  hsTimeline: Time Line
  hsLineTree: LineTree
  hsList: List Page
  hsListCard: Card List Page
  hsDebounce: Debounce & Throttle
  hsFormDesign: Form Design
  hsBarcode: Barcode
  hsQrcode: Qrcode
  hsCascader: Area Cascader
  hsSwiper: Swiper Plugin
  hsVirtualList: Virtual List
  hsPdf: PDF Preview
  hsExcel: Export Excel
  hsInfiniteScroll: Table Infinite Scroll
  hsSensitive: Sensitive Filter
  hsPinyin: PinYin
  hsdanmaku: Danmaku Components
  hsPureTableBase: Base Usage
  hsPureTableHigh: High Usage
  hsTree: Big Data Tree
  hsMenuoverflow: Menu Overflow Show Tooltip Text
  hsChildMenuoverflow: Child Menu Overflow Show Tooltip Text
  hslianxi: lianxi

status:
  hsLoad: Loading...
login:
  username: Username
  password: Password
  verifyCode: VerifyCode
  remember: days no need to login
  rememberInfo: After checking and logging in, will automatically log in to the system without entering your username and password within the specified number of days.
  sure: Sure Password
  forget: Forget Password?
  login: Login
  thirdLogin: Third Login
  phoneLogin: Phone Login
  qRCodeLogin: QRCode Login
  register: Register
  weChatLogin: WeChat Login
  alipayLogin: Alipay Login
  qqLogin: QQ Login
  weiboLogin: Weibo Login
  phone: Phone
  smsVerifyCode: SMS VerifyCode
  back: Back
  test: Mock Test
  tip: After scanning the code, click "Confirm" to complete the login
  definite: Definite
  loginSuccess: Login Success
  registerSuccess: Regist Success
  tickPrivacy: Please tick Privacy Policy
  readAccept: I have read it carefully and accept
  privacyPolicy: Privacy Policy
  getVerifyCode: Get VerifyCode
  info: Seconds
  usernameReg: Please enter username
  passwordReg: Please enter password
  verifyCodeReg: Please enter verify code
  verifyCodeCorrectReg: Please enter correct verify code
  verifyCodeSixReg: Please enter a 6-digit verify code
  phoneReg: Please enter the phone
  phoneCorrectReg: Please enter the correct phone number format
  passwordRuleReg: The password format should be any combination of 8-18 digits
  passwordSureReg: Please enter confirm password
  passwordDifferentReg: The two passwords do not match!
  passwordUpdateReg: Password has been updated
