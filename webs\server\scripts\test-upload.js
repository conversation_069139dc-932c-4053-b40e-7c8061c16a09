require('dotenv').config();
const fs = require('fs');
const path = require('path');
const picxService = require('../src/services/picxService');

// 测试图片上传功能
async function testImageUpload() {
  try {
    console.log('开始测试图片上传...');

    // 读取测试图片文件
    // 注意：请确保在项目根目录下有一个test.jpg文件，或者修改为您系统中存在的图片路径
    const imagePath = path.join(__dirname, 'test.jpg');
    console.log(`读取图片: ${imagePath}`);

    if (!fs.existsSync(imagePath)) {
      console.error('测试图片不存在，请确保路径正确');
      return;
    }

    const imageBuffer = fs.readFileSync(imagePath);
    console.log(`图片大小: ${imageBuffer.length} 字节`);

    // 上传图片
    console.log('正在上传图片...');
    const imageUrl = await picxService.uploadImage(imageBuffer, 'test.jpg');

    console.log('图片上传成功!');
    console.log(`图片URL: ${imageUrl}`);
  } catch (error) {
    console.error('图片上传测试失败:', error);
  }
}

// 执行测试
testImageUpload();
