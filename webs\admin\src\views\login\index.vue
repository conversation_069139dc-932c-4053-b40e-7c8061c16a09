<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>

    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <el-icon size="48" color="#409eff">
            <House />
          </el-icon>
        </div>
        <h1>楠楠家厨管理系统</h1>
        <p>欢迎登录后台管理系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名/手机号"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-button type="text" @click="handleForgetPassword"
              >忘记密码？</el-button
            >
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-btn"
          >
            <el-icon v-if="!loading"><User /></el-icon>
            {{ loading ? "登录中..." : "登录" }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-tips">
        <el-divider>
          <span class="divider-text">测试账号</span>
        </el-divider>
        <div class="test-accounts">
          <div class="account-item" @click="fillTestAccount('admin')">
            <el-tag type="danger" size="small">管理员</el-tag>
            <span>*********** / 123456</span>
          </div>
          <div class="account-item" @click="fillTestAccount('user')">
            <el-tag type="primary" size="small">普通用户</el-tag>
            <span>*********** / 123456</span>
          </div>
        </div>
      </div>

      <div class="login-footer">
        <p>&copy; 2024 楠楠家厨管理系统. All rights reserved.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { User, Lock, House } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/user";

const router = useRouter();
const userStore = useUserStore();

const loginFormRef = ref();
const loading = ref(false);
const rememberMe = ref(false);

const loginForm = reactive({
  username: "",
  password: ""
});

const loginRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 3, max: 20, message: "用户名长度在 3 到 20 个字符", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
  ]
};

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    loading.value = true;

    const result = await userStore.login({
      username: loginForm.username,
      password: loginForm.password,
      loginType: "password"
    });

    if (result.success) {
      ElMessage.success("登录成功");

      // 记住用户名
      if (rememberMe.value) {
        localStorage.setItem("remembered_username", loginForm.username);
      } else {
        localStorage.removeItem("remembered_username");
      }

      // 等待状态更新后跳转
      await nextTick();

      // 跳转到首页
      setTimeout(() => {
        router.push("/");
      }, 100);
    } else {
      ElMessage.error(result.message || "登录失败");
    }
  } catch (error) {
    console.error("登录错误:", error);
    ElMessage.error("登录失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 忘记密码
const handleForgetPassword = () => {
  ElMessageBox.alert(
    "请联系系统管理员重置密码，或使用测试账号进行登录。",
    "忘记密码",
    {
      confirmButtonText: "我知道了",
      type: "info"
    }
  );
};

// 填充测试账号
const fillTestAccount = type => {
  if (type === "admin") {
    loginForm.username = "***********";
    loginForm.password = "123456";
  } else {
    loginForm.username = "***********";
    loginForm.password = "123456";
  }
};

// 初始化
onMounted(() => {
  // 检查是否已登录
  if (userStore.isLoggedIn) {
    router.push("/");
    return;
  }

  // 恢复记住的用户名
  const rememberedUsername = localStorage.getItem("remembered_username");
  if (rememberedUsername) {
    loginForm.username = rememberedUsername;
    rememberMe.value = true;
  }
});
</script>

<style scoped lang="scss">
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-box {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.05)
    );
    border-radius: 20px;
    z-index: -1;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  .logo {
    margin-bottom: 16px;
  }

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  p {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  :deep(.el-input__wrapper) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.is-focus {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;

  :deep(.el-button--text) {
    color: #409eff;
    font-size: 14px;

    &:hover {
      color: #66b1ff;
    }
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border: none;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #66b1ff, #409eff);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.login-tips {
  margin-top: 30px;

  .divider-text {
    color: #999;
    font-size: 12px;
    padding: 0 16px;
    background: rgba(255, 255, 255, 0.95);
  }

  .test-accounts {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .account-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    background: rgba(64, 158, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(64, 158, 255, 0.1);
      transform: translateX(4px);
    }

    span {
      font-size: 12px;
      color: #666;
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 30px;

  p {
    color: #999;
    font-size: 12px;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 20px;
  }

  .login-box {
    padding: 30px 20px;
    max-width: 100%;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .bg-shape {
    &.shape-1 {
      width: 150px;
      height: 150px;
    }

    &.shape-2 {
      width: 100px;
      height: 100px;
    }

    &.shape-3 {
      width: 80px;
      height: 80px;
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .login-box {
    background: rgba(30, 30, 30, 0.95);

    .login-header {
      h1 {
        color: #fff;
      }

      p {
        color: #ccc;
      }
    }

    .divider-text {
      color: #ccc;
      background: rgba(30, 30, 30, 0.95);
    }

    .account-item {
      background: rgba(64, 158, 255, 0.1);

      span {
        color: #ccc;
      }
    }

    .login-footer p {
      color: #ccc;
    }
  }
}
</style>
