<template>
  <div class="dish-selector">
    <!-- 分类标签 -->
    <div class="category-tabs">
      <el-button
        v-for="category in categories"
        :key="category"
        :type="activeCategory === category ? 'primary' : 'default'"
        @click="activeCategory = category"
        size="small"
      >
        {{ category }}
      </el-button>
    </div>
    
    <!-- 菜品列表 -->
    <div class="dish-list">
      <div
        v-for="dish in filteredDishes"
        :key="dish.id"
        class="dish-item"
        :class="{ selected: isSelected(dish.id) }"
        @click="toggleDish(dish)"
      >
        <div class="dish-info">
          <img :src="dish.image || '/default-dish.jpg'" :alt="dish.name" class="dish-image" />
          <div class="dish-details">
            <h4>{{ dish.name }}</h4>
            <p>{{ dish.description }}</p>
            <span class="dish-category">{{ dish.category }}</span>
          </div>
        </div>
        
        <div class="dish-actions" v-if="isSelected(dish.id)">
          <el-input-number
            v-model="getSelectedDish(dish.id).count"
            :min="1"
            :max="10"
            size="small"
            @click.stop
          />
        </div>
      </div>
    </div>
    
    <!-- 已选择的菜品 -->
    <div class="selected-summary" v-if="localSelectedDishes.length > 0">
      <h4>已选择菜品 ({{ localSelectedDishes.length }})</h4>
      <div class="selected-list">
        <el-tag
          v-for="dish in localSelectedDishes"
          :key="dish.id"
          closable
          @close="removeDish(dish.id)"
          class="selected-tag"
        >
          {{ dish.name }} x{{ dish.count }}
        </el-tag>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="selector-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">
        确定 ({{ localSelectedDishes.length }})
      </el-button>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue'
import { dishApi } from '@/api/menu'

export default defineComponent({
  name: 'DishSelector',
  props: {
    selectedDishes: {
      type: Array,
      default: () => []
    }
  },
  emits: ['confirm', 'cancel'],
  setup(props, { emit }) {
    const dishes = ref([])
    const activeCategory = ref('全部')
    const localSelectedDishes = ref([])
    
    // 分类列表
    const categories = computed(() => {
      const cats = ['全部']
      const dishCategories = [...new Set(dishes.value.map(dish => dish.category))]
      return cats.concat(dishCategories)
    })
    
    // 过滤后的菜品
    const filteredDishes = computed(() => {
      if (activeCategory.value === '全部') {
        return dishes.value
      }
      return dishes.value.filter(dish => dish.category === activeCategory.value)
    })
    
    // 加载菜品数据
    const loadDishes = async () => {
      try {
        const response = await dishApi.getDishes()
        if (response.data) {
          dishes.value = response.data
        }
      } catch (error) {
        console.error('加载菜品失败:', error)
      }
    }
    
    // 初始化已选择的菜品
    const initSelectedDishes = () => {
      localSelectedDishes.value = props.selectedDishes.map(dish => ({
        ...dish,
        count: dish.count || 1
      }))
    }
    
    // 检查菜品是否已选择
    const isSelected = (dishId) => {
      return localSelectedDishes.value.some(dish => dish.id === dishId)
    }
    
    // 获取已选择的菜品
    const getSelectedDish = (dishId) => {
      return localSelectedDishes.value.find(dish => dish.id === dishId)
    }
    
    // 切换菜品选择状态
    const toggleDish = (dish) => {
      const index = localSelectedDishes.value.findIndex(item => item.id === dish.id)
      
      if (index > -1) {
        // 已选择，移除
        localSelectedDishes.value.splice(index, 1)
      } else {
        // 未选择，添加
        localSelectedDishes.value.push({
          id: dish.id,
          name: dish.name,
          count: 1
        })
      }
    }
    
    // 移除菜品
    const removeDish = (dishId) => {
      const index = localSelectedDishes.value.findIndex(dish => dish.id === dishId)
      if (index > -1) {
        localSelectedDishes.value.splice(index, 1)
      }
    }
    
    // 确认选择
    const handleConfirm = () => {
      emit('confirm', localSelectedDishes.value)
    }
    
    // 取消选择
    const handleCancel = () => {
      emit('cancel')
    }
    
    onMounted(() => {
      loadDishes()
      initSelectedDishes()
    })
    
    return {
      dishes,
      activeCategory,
      categories,
      filteredDishes,
      localSelectedDishes,
      isSelected,
      getSelectedDish,
      toggleDish,
      removeDish,
      handleConfirm,
      handleCancel
    }
  }
})
</script>

<style scoped>
.dish-selector {
  max-height: 600px;
  overflow-y: auto;
}

.category-tabs {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.category-tabs .el-button {
  margin-right: 10px;
  margin-bottom: 5px;
}

.dish-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.dish-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.dish-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.dish-item.selected {
  border-color: #409eff;
  background-color: #e6f7ff;
}

.dish-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.dish-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  object-fit: cover;
  margin-right: 12px;
}

.dish-details h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
}

.dish-details p {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.dish-category {
  font-size: 12px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.dish-actions {
  margin-left: 12px;
}

.selected-summary {
  margin-bottom: 20px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
}

.selected-summary h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag {
  margin: 0;
}

.selector-actions {
  text-align: right;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.selector-actions .el-button {
  margin-left: 10px;
}
</style>
