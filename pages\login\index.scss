/* 登录页面 - Tailwind CSS 风格 */

/* 确保容器样式正确应用 */
.container {
  min-height: 100vh;
  padding: 32rpx;
  padding-top: 120rpx;
  background-color: var(--gray-50);
}

/* 标题样式增强 */
.text-2xl.font-bold.text-gray-900 {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1.2;
}

.text-sm.text-gray-500 {
  font-size: 26rpx;
  color: var(--gray-500);
}

/* 卡片样式增强 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: var(--shadow-md);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

/* Tab 切换样式 */
.tab-bar {
  display: flex;
  background: var(--gray-100);
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: var(--gray-600);
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-item.active {
  background-color: var(--primary-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

/* 输入框组样式 */
.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  font-size: 28rpx;
  color: var(--gray-700);
  margin-bottom: 12rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid var(--gray-200);
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: white;
  transition: all 0.2s ease;
  color: var(--gray-900);
}

.input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

/* 按钮样式增强 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
}

.btn-primary:active {
  background-color: var(--primary-700);
  transform: translateY(1rpx);
}

.btn-primary.opacity-50 {
  opacity: 0.5;
  pointer-events: none;
}

/* 微信登录按钮 */
.bg-green {
  background-color: var(--green-500) !important;
}

.bg-green:active {
  background-color: var(--green-600) !important;
}

/* 微信头像圆形背景 */
.w-32.h-32.bg-green.rounded-full {
  width: 128rpx;
  height: 128rpx;
  background-color: var(--green-500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  box-shadow: var(--shadow-md);
}

/* 记住密码和忘记密码区域 */
.flex.justify-between.items-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.flex.items-center {
  display: flex;
  align-items: center;
}

/* 复选框样式 */
checkbox {
  margin-right: 16rpx;
}

/* 链接文本样式 */
.text-primary {
  color: var(--primary-600);
  font-weight: 500;
}

.text-primary:active {
  color: var(--primary-700);
}

/* 错误提示样式 */
.text-red {
  color: var(--red-500);
  font-weight: 500;
}

/* 文本居中 */
.text-center {
  text-align: center;
}

/* 间距工具类 */
.mb-2 {
  margin-bottom: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

.ml-1 {
  margin-left: 8rpx;
}

.ml-2 {
  margin-left: 16rpx;
}

/* 宽度工具类 */
.w-full {
  width: 100%;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .container {
    padding: 24rpx;
    padding-top: 100rpx;
  }

  .card {
    padding: 24rpx;
  }

  .tab-item {
    font-size: 24rpx;
    padding: 14rpx 8rpx;
  }
}
