/**
 * 通用工具函数
 */

import {ElMessage, ElMessageBox} from 'element-plus';
import dayjs from 'dayjs';

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @param {boolean} immediate 是否立即执行
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔
 */
export function throttle(func, limit) {
  let inThrottle;
  return function (...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 深拷贝
 * @param {any} obj 要拷贝的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @param {number} decimals 小数位数
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化数字
 * @param {number} num 数字
 * @param {number} precision 精度
 */
export function formatNumber(num, precision = 2) {
  if (num >= 100000000) {
    return (num / 100000000).toFixed(precision) + '亿';
  } else if (num >= 10000) {
    return (num / 10000).toFixed(precision) + '万';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(precision) + 'k';
  }
  return num.toString();
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @param {string} format 格式
 */
export function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return '';
  return dayjs(time).format(format);
}

/**
 * 获取相对时间
 * @param {string|Date} time 时间
 */
export function getRelativeTime(time) {
  if (!time) return '';

  const now = dayjs();
  const target = dayjs(time);
  const diff = now.diff(target, 'minute');

  if (diff < 1) return '刚刚';
  if (diff < 60) return `${diff}分钟前`;
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`;
  if (diff < 10080) return `${Math.floor(diff / 1440)}天前`;

  return target.format('YYYY-MM-DD');
}

/**
 * 生成随机字符串
 * @param {number} length 长度
 */
export function generateRandomString(length = 8) {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 验证手机号
 * @param {string} phone 手机号
 */
export function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 */
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 */
export function validateIdCard(idCard) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
}

/**
 * 获取URL参数
 * @param {string} name 参数名
 * @param {string} url URL地址
 */
export function getUrlParam(name, url = window.location.href) {
  const regex = new RegExp('[?&]' + name + '=([^&#]*)', 'i');
  const match = regex.exec(url);
  return match ? decodeURIComponent(match[1]) : null;
}

/**
 * 设置URL参数
 * @param {string} name 参数名
 * @param {string} value 参数值
 */
export function setUrlParam(name, value) {
  const url = new URL(window.location.href);
  url.searchParams.set(name, value);
  window.history.replaceState({}, '', url.toString());
}

/**
 * 下载文件
 * @param {string} url 文件URL
 * @param {string} filename 文件名
 */
export function downloadFile(url, filename) {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * 复制到剪贴板
 * @param {string} text 要复制的文本
 */
export async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success('复制成功');
    return true;
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
    return false;
  }
}

/**
 * 确认对话框
 * @param {string} message 消息
 * @param {string} title 标题
 * @param {string} type 类型
 */
export function confirmDialog(message, title = '确认', type = 'warning') {
  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type
  });
}

/**
 * 成功提示
 * @param {string} message 消息
 */
export function successMessage(message) {
  ElMessage.success(message);
}

/**
 * 错误提示
 * @param {string} message 消息
 */
export function errorMessage(message) {
  ElMessage.error(message);
}

/**
 * 警告提示
 * @param {string} message 消息
 */
export function warningMessage(message) {
  ElMessage.warning(message);
}

/**
 * 信息提示
 * @param {string} message 消息
 */
export function infoMessage(message) {
  ElMessage.info(message);
}

/**
 * 获取图片预览URL
 * @param {File} file 文件对象
 */
export function getImagePreviewUrl(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => resolve(e.target.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * 压缩图片
 * @param {File} file 图片文件
 * @param {number} quality 压缩质量 0-1
 * @param {number} maxWidth 最大宽度
 */
export function compressImage(file, quality = 0.8, maxWidth = 800) {
  return new Promise(resolve => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;

      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      canvas.toBlob(resolve, 'image/jpeg', quality);
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * 滚动到顶部
 * @param {number} duration 动画时长
 */
export function scrollToTop(duration = 300) {
  const start = window.pageYOffset;
  const startTime = performance.now();

  function scroll() {
    const now = performance.now();
    const time = Math.min(1, (now - startTime) / duration);
    const timeFunction = time * (2 - time); // easeOutQuad

    window.scroll(0, Math.ceil((1 - timeFunction) * start));

    if (time < 1) {
      requestAnimationFrame(scroll);
    }
  }

  requestAnimationFrame(scroll);
}

/**
 * 检测设备类型
 */
export function getDeviceType() {
  const ua = navigator.userAgent;

  if (/tablet|ipad|playbook|silk/i.test(ua)) {
    return 'tablet';
  }

  if (
    /mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(
      ua
    )
  ) {
    return 'mobile';
  }

  return 'desktop';
}

/**
 * 获取浏览器信息
 */
export function getBrowserInfo() {
  const ua = navigator.userAgent;
  let browser = 'Unknown';
  let version = 'Unknown';

  if (ua.indexOf('Chrome') > -1) {
    browser = 'Chrome';
    version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
  } else if (ua.indexOf('Firefox') > -1) {
    browser = 'Firefox';
    version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
  } else if (ua.indexOf('Safari') > -1) {
    browser = 'Safari';
    version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
  } else if (ua.indexOf('Edge') > -1) {
    browser = 'Edge';
    version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
  }

  return {browser, version};
}

/**
 * 获取分类文本
 * @param {string} category 分类值
 */
export function getCategoryText(category) {
  const categoryMap = {
    main: '主菜',
    soup: '汤品',
    dessert: '甜品',
    drink: '饮品',
    appetizer: '开胃菜',
    staple: '主食',
    vegetable: '蔬菜',
    meat: '肉类',
    seafood: '海鲜'
  };
  return categoryMap[category] || category;
}

/**
 * 获取分类类型
 * @param {string} category 分类值
 */
export function getCategoryType(category) {
  const typeMap = {
    main: 'primary',
    soup: 'info',
    dessert: 'warning',
    drink: 'success',
    appetizer: 'danger',
    staple: 'primary',
    vegetable: 'success',
    meat: 'danger',
    seafood: 'info'
  };
  return typeMap[category] || 'default';
}

/**
 * 获取订单状态文本
 * @param {string} status 状态值
 */
export function getOrderStatusText(status) {
  const statusMap = {
    pending: '待处理',
    confirmed: '已确认',
    preparing: '准备中',
    ready: '已完成',
    delivered: '已送达',
    cancelled: '已取消',
    paid: '已支付',
    unpaid: '未支付'
  };
  return statusMap[status] || status;
}

/**
 * 获取订单状态类型
 * @param {string} status 状态值
 */
export function getOrderStatusType(status) {
  const typeMap = {
    pending: 'warning',
    confirmed: 'primary',
    preparing: 'info',
    ready: 'success',
    delivered: 'success',
    cancelled: 'danger',
    paid: 'success',
    unpaid: 'warning'
  };
  return typeMap[status] || 'default';
}

export default {
  debounce,
  throttle,
  deepClone,
  formatFileSize,
  formatNumber,
  formatTime,
  getRelativeTime,
  generateRandomString,
  validatePhone,
  validateEmail,
  validateIdCard,
  getUrlParam,
  setUrlParam,
  downloadFile,
  copyToClipboard,
  confirmDialog,
  successMessage,
  errorMessage,
  warningMessage,
  infoMessage,
  getImagePreviewUrl,
  compressImage,
  scrollToTop,
  getDeviceType,
  getBrowserInfo,
  getCategoryText,
  getCategoryType,
  getOrderStatusText,
  getOrderStatusType
};
