/* 现代化设计 */
@import "../../styles/modern-design.scss";

/* 点菜页面 - Tailwind CSS 风格 */

.container {
  @extend .page-container;
  @extend .page-container-safe;
}

.dish-page {
  @extend .flex;
  gap: 24rpx;
  height: calc(100vh - 120rpx);
  overflow: hidden;
}

/* 侧边导航 */
.side-nav {
  width: 160rpx;
  @extend .modern-card;
  @extend .flex;
  @extend .flex-col;
  @extend .gap-2;
  @extend .shadow-md;
  height: 100%;
  flex-shrink: 0;
  @extend .overflow-auto;
}

.nav-item {
  @extend .modern-btn;
  @extend .btn-secondary;
  @extend .text-sm;
  width: 100%;

  &:active {
    @extend .bg-gray-100;
  }

  &.active {
    @extend .btn-primary;
  }
}

/* 菜品列表区域 */
.food-list-area {
  @extend .flex-1;
  @extend .modern-card;
  height: 100%;
  @extend .overflow-auto;
}

.food-list {
  @extend .flex;
  @extend .flex-col;
  gap: 20rpx;
  margin-top: 24rpx;
}

/* 菜品卡片 */
.food-card {
  @extend .modern-card;
  @extend .card-flat;
  @extend .flex;
  @extend .items-center;
  @extend .gap-3;
  @extend .p-3;
  @extend .mb-2;
  @extend .transition;

  &:active {
    transform: translateY(-2rpx);
    @extend .shadow-lg;
  }
}

.food-img {
  width: 120rpx;
  height: 120rpx;
  @extend .rounded-lg;
  @extend .shadow-sm;
  flex-shrink: 0;
  border: 2rpx solid #6366f1;
  object-fit: cover;
}

.food-info {
  @extend .flex-1;
  @extend .flex;
  @extend .flex-col;
  @extend .justify-center;
}

.food-name {
  @extend .text-lg;
  @extend .font-bold;
  @extend .text-primary;
  @extend .mb-1;
  text-align: left;
}

.food-desc {
  @extend .text-gray-600;
  @extend .text-sm;
  @extend .mb-2;
  text-align: left;
  min-height: 1.8em;
}

.food-card-bottom {
  @extend .flex;
  @extend .justify-between;
  @extend .items-center;
  width: 100%;
}

.food-price {
  @extend .text-secondary;
  @extend .font-bold;
  @extend .text-lg;
}

.order-btn {
  @extend .modern-btn;
  @extend .btn-primary;
  @extend .btn-sm;
  min-width: 80rpx;

  &.adding {
    @extend .btn-success;
    transform: scale(1.05);
  }

  .success-icon {
    @extend .text-white;
    @extend .font-bold;
  }
}

.order-btn-hover {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 添加食物卡片的悬停效果 */
.food-card {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8px 24px 0 rgba(0, 242, 234, 0.2);
  }
}

.basket-fab {
  position: fixed;
  right: 44rpx;
  bottom: 44rpx;
  z-index: 100;
  @extend .modern-btn;
  @extend .btn-primary;
  color: #111827;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  @extend .flex;
  @extend .items-center;
  @extend .justify-center;
  box-shadow: 0 4px 24px rgba(0, 242, 234, 0.33);
}

.basket-count {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #fe2c55;
  color: #111827;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  @extend .flex;
  @extend .items-center;
  @extend .justify-center;
  font-size: 24rpx;
  font-weight: bold;
  border: 4rpx solid #fff;
  box-shadow: 0 1px 4px rgba(254, 44, 85, 0.2);
}

/* 点菜页面特定样式 */
.dish-page {
  @extend .flex;
  @extend .gap-3;
  height: calc(100vh - 120rpx);
  @extend .overflow-hidden;
}

.side-nav {
  width: 160rpx;
  @extend .modern-card;
  @extend .flex;
  @extend .flex-col;
  @extend .gap-2;
  height: 100%;
  flex-shrink: 0;
  @extend .overflow-auto;
}

.nav-item {
  @extend .modern-btn;
  @extend .btn-secondary;
  @extend .text-sm;

  &.active {
    @extend .btn-primary;
  }
}

.food-list-area {
  @extend .flex-1;
  @extend .modern-card;
  height: 100%;
  @extend .overflow-auto;
}

.food-item {
  @extend .modern-card;
  @extend .card-flat;
  @extend .flex;
  @extend .items-center;
  @extend .gap-3;
  @extend .p-3;
  @extend .mb-2;
}
