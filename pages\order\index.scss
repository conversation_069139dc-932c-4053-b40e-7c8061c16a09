.container {
  background: #181a20;
  color: #f5f6fa;
  min-height: 100vh;
  padding: 20rpx;
}

.dish-page {
  display: flex;
  flex-direction: row;
  gap: 24rpx;
  min-height: 80vh;
}

.side-nav {
  width: 160rpx;
  background: #20232a;
  border-radius: 24rpx;
  padding: 24rpx 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  box-shadow: 0 2px 12px rgba(0, 242, 234, 0.13);
  margin-right: 20rpx;
  height: 100%;
  min-height: 80vh;
}

.nav-item {
  padding: 14rpx 4rpx;
  width: 100%;
  text-align: center;
  color: #b3e0f7;
  border-radius: 16rpx;
  margin: 2rpx 0;
  font-size: 28rpx;
  font-weight: 500;
}

.nav-item.active {
  background: linear-gradient(90deg, #00f2ea 0%, #fe2c55 100%);
  color: #fff;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 242, 234, 0.2);
}

.food-list-area {
  flex: 1;
  min-width: 0;
}

.food-list {
  display: flex;
  flex-direction: column;
  gap: 22rpx;
  margin-top: 24rpx;
}

.food-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: linear-gradient(120deg, #23272f 60%, #23233b 100%);
  border-radius: 24rpx;
  box-shadow: 0 4px 18px 0 rgba(0, 242, 234, 0.13);
  padding: 14rpx 22rpx 14rpx 14rpx;
  position: relative;
  overflow: hidden;
}

.food-img {
  width: 140rpx;
  height: 140rpx;
  border-radius: 20rpx;
  margin-right: 22rpx;
  box-shadow: 0 2px 8px rgba(0, 242, 234, 0.2);
  flex-shrink: 0;
  border: 4rpx solid rgba(0, 242, 234, 0.2);
}

.food-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.food-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #00f2ea;
  margin-bottom: 4rpx;
  text-align: left;
  letter-spacing: 1rpx;
}

.food-desc {
  color: #b3e0f7;
  font-size: 26rpx;
  margin-bottom: 10rpx;
  text-align: left;
  min-height: 1.8em;
}

.food-card-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.food-price {
  color: #00f2ea;
  font-weight: bold;
  font-size: 30rpx;
  letter-spacing: 1rpx;
}

.order-btn {
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  font-weight: bold;
  padding: 8rpx 22rpx;
  border-radius: 24rpx;
  box-shadow: 0 2px 8px rgba(254, 44, 85, 0.2);
  font-size: 26rpx;
  border: none;
  line-height: 1.6;
  margin: 0;
  min-width: 100rpx; /* 添加最小宽度 */
  display: flex; /* 使用 flex 布局 */
  align-items: center;
  justify-content: center; /* 文字居中 */
  transition: all 0.3s ease; /* 添加过渡效果 */

  &.adding {
    background: #00f2ea;
    transform: scale(1.05);
  }

  .success-icon {
    color: #fff;
    font-weight: bold;
  }
}

.order-btn-hover {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 添加食物卡片的悬停效果 */
.food-card {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8px 24px 0 rgba(0, 242, 234, 0.2);
  }
}

.basket-fab {
  position: fixed;
  right: 44rpx;
  bottom: 44rpx;
  z-index: 100;
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 24px rgba(0, 242, 234, 0.33);
}

.basket-count {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #fe2c55;
  color: #fff;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  border: 4rpx solid #fff;
  box-shadow: 0 1px 4px rgba(254, 44, 85, 0.2);
}
