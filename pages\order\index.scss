/* 点菜页面 - Tailwind CSS 风格 */

.container {
  background-color: #111827;
  color: #f3f4f6;
  min-height: 100vh;
  padding: 24rpx;
}

.dish-page {
  display: flex;
  gap: 24rpx;
  min-height: 80vh;
}

/* 侧边导航 */
.side-nav {
  width: 160rpx;
  background-color: #1f2937;
  border-radius: 16rpx;
  padding: 24rpx 12rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-height: 80vh;
}

.nav-item {
  padding: 16rpx 8rpx;
  width: 100%;
  text-align: center;
  color: #9ca3af;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.nav-item:hover {
  background-color: #374151;
  color: #e5e7eb;
}

.nav-item.active {
  background: linear-gradient(135deg, #3b82f6, #ec4899);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 菜品列表区域 */
.food-list-area {
  flex: 1;
  min-width: 0;
}

.food-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 24rpx;
}

/* 菜品卡片 */
.food-card {
  display: flex;
  align-items: center;
  background-color: #1f2937;
  border-radius: 16rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  position: relative;
  overflow: hidden;
  border: 1rpx solid #374151;
  transition: all 0.2s ease;
}

.food-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #3b82f6;
}

.food-img {
  width: 140rpx;
  height: 140rpx;
  border-radius: 20rpx;
  margin-right: 22rpx;
  box-shadow: 0 2px 8px rgba(0, 242, 234, 0.2);
  flex-shrink: 0;
  border: 4rpx solid rgba(0, 242, 234, 0.2);
}

.food-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.food-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #00f2ea;
  margin-bottom: 4rpx;
  text-align: left;
  letter-spacing: 1rpx;
}

.food-desc {
  color: #b3e0f7;
  font-size: 26rpx;
  margin-bottom: 10rpx;
  text-align: left;
  min-height: 1.8em;
}

.food-card-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.food-price {
  color: #00f2ea;
  font-weight: bold;
  font-size: 30rpx;
  letter-spacing: 1rpx;
}

.order-btn {
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  font-weight: bold;
  padding: 8rpx 22rpx;
  border-radius: 24rpx;
  box-shadow: 0 2px 8px rgba(254, 44, 85, 0.2);
  font-size: 26rpx;
  border: none;
  line-height: 1.6;
  margin: 0;
  min-width: 100rpx; /* 添加最小宽度 */
  display: flex; /* 使用 flex 布局 */
  align-items: center;
  justify-content: center; /* 文字居中 */
  transition: all 0.3s ease; /* 添加过渡效果 */

  &.adding {
    background: #00f2ea;
    transform: scale(1.05);
  }

  .success-icon {
    color: #fff;
    font-weight: bold;
  }
}

.order-btn-hover {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 添加食物卡片的悬停效果 */
.food-card {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8px 24px 0 rgba(0, 242, 234, 0.2);
  }
}

.basket-fab {
  position: fixed;
  right: 44rpx;
  bottom: 44rpx;
  z-index: 100;
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 24px rgba(0, 242, 234, 0.33);
}

.basket-count {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #fe2c55;
  color: #fff;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  border: 4rpx solid #fff;
  box-shadow: 0 1px 4px rgba(254, 44, 85, 0.2);
}
