/* 统计页面 - Tailwind CSS 风格 */

.container {
  background-color: var(--gray-900);
  min-height: 100vh;
  padding: 32rpx 24rpx;
}

.main-card {
  background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
  border-radius: 24rpx;
  box-shadow: var(--shadow-2xl);
  margin-bottom: 32rpx;
  padding: 48rpx 32rpx;
  border: 2rpx solid var(--gray-600);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, var(--pink-500), var(--primary-500));
    border-radius: 24rpx 24rpx 0 0;
  }
}

/* 标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--pink-500);
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 统计条样式 */
.stat-bar {
  background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  box-shadow: var(--shadow-lg);
  border: 1rpx solid var(--gray-600);

  text:last-child {
    color: var(--primary-500);
    font-weight: 700;
  }

  &.green {
    color: var(--green-500);
  }

  &.orange {
    color: var(--orange-500);
  }

  &.red {
    color: var(--red-500);
  }
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mt-6 {
  margin-top: 48rpx;
}

.hot-rank-list {
  display: flex;
  flex-direction: column;
  gap: 14rpx;
}

.hot-rank-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 14rpx;
}

.rank-number {
  font-size: 36rpx;
  font-weight: bold;
  width: 60rpx;
  text-align: center;

  &.red {
    color: #fe2c55;
  }

  &.orange {
    color: #ffa726;
  }

  &.cyan {
    color: #00f2ea;
  }
}

.rank-img {
  width: 76rpx;
  height: 76rpx;
  border-radius: 20rpx;
  object-fit: cover;
  border: 4rpx solid rgba(0, 242, 234, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 242, 234, 0.13);
}

.rank-name {
  flex: 1;
  font-size: 32rpx;
}

.rank-count {
  font-size: 30rpx;
  color: #b3e0f7;
}
