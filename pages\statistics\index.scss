.container {
  background: #121212;
  min-height: 100vh;
  padding: 40rpx 20rpx;
}

.main-card {
  background: linear-gradient(120deg, #1f1f1f 60%, #2a2a2a 100%);
  border-radius: 44rpx;
  box-shadow:
    0 12rpx 64rpx rgba(0, 0, 0, 0.6),
    0 3rpx 0 #232323 inset;
  margin-bottom: 56rpx;
  padding: 56rpx 40rpx;
  border: 3rpx solid #232323;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
    border-radius: 44rpx 44rpx 0 0;
    opacity: 0.8;
  }
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fe2c55;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  letter-spacing: 2rpx;
}

.icon-margin {
  margin-right: 16rpx;
}

.stat-bar {
  background: linear-gradient(90deg, #232323 60%, #2a2a2a 100%);
  border-radius: 28rpx;
  padding: 32rpx 36rpx;
  margin-bottom: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);

  text:last-child {
    color: #00f2ea;
    font-weight: 700;
  }

  &.green {
    color: #00f2ea;
  }

  &.orange {
    color: #ffa726;
  }

  &.red {
    color: #fe2c55;
  }
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mt-6 {
  margin-top: 48rpx;
}

.hot-rank-list {
  display: flex;
  flex-direction: column;
  gap: 14rpx;
}

.hot-rank-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 14rpx;
}

.rank-number {
  font-size: 36rpx;
  font-weight: bold;
  width: 60rpx;
  text-align: center;

  &.red {
    color: #fe2c55;
  }

  &.orange {
    color: #ffa726;
  }

  &.cyan {
    color: #00f2ea;
  }
}

.rank-img {
  width: 76rpx;
  height: 76rpx;
  border-radius: 20rpx;
  object-fit: cover;
  border: 4rpx solid rgba(0, 242, 234, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 242, 234, 0.13);
}

.rank-name {
  flex: 1;
  font-size: 32rpx;
}

.rank-count {
  font-size: 30rpx;
  color: #b3e0f7;
}
