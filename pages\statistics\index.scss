/* 统计页面 - Tailwind CSS 风格 */

.container {
  background-color: #111827;
  min-height: 100vh;
  padding: 32rpx 24rpx;
}

.main-card {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 24rpx;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  margin-bottom: 32rpx;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #ec4899, #3b82f6);
    border-radius: 24rpx 24rpx 0 0;
  }
}

/* 标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ec4899;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 统计条样式 */
.stat-bar {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1rpx solid #4b5563;

  text:last-child {
    color: #3b82f6;
    font-weight: 700;
  }

  &.green {
    color: #10b981;
  }

  &.orange {
    color: #f97316;
  }

  &.red {
    color: #ef4444;
  }
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mt-6 {
  margin-top: 48rpx;
}

.hot-rank-list {
  display: flex;
  flex-direction: column;
  gap: 14rpx;
}

.hot-rank-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 14rpx;
}

.rank-number {
  font-size: 36rpx;
  font-weight: bold;
  width: 60rpx;
  text-align: center;

  &.red {
    color: #fe2c55;
  }

  &.orange {
    color: #ffa726;
  }

  &.cyan {
    color: #00f2ea;
  }
}

.rank-img {
  width: 76rpx;
  height: 76rpx;
  border-radius: 20rpx;
  object-fit: cover;
  border: 4rpx solid rgba(0, 242, 234, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 242, 234, 0.13);
}

.rank-name {
  flex: 1;
  font-size: 32rpx;
}

.rank-count {
  font-size: 30rpx;
  color: #b3e0f7;
}
