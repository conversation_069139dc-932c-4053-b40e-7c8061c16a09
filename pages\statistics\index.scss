/* 现代化设计 */
@import "../../styles/miniprogram-design.scss";

/* 统计页面 - Tailwind CSS 风格 */

.container {
  @include page-container;
  @include page-container-safe;
}

.main-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @include rounded-lg;
  @include shadow-md;
  @include mb-4;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    @include modern-btn; @include btn-primary;
    @extend .rounded-lg 24rpx 0 0;
  }
}

/* 标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  @include text-primary;
  @include mb-4;
  @include flex;
  @include items-center;
  gap: 12rpx;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 统计条样式 */
.stat-bar {
  background: linear-gradient(135deg, white, #f3f4f6);
  @include rounded-md;
  @extend .p-3 32rpx;
  @include mb-3;
  @include flex;
  @include justify-between;
  @include items-center;
  font-size: 28rpx;
  font-weight: 600;
  color: #111827;
  @include shadow-md;
  border: 1rpx solid #4b5563;

  text:last-child {
    color: $primary-solid;
    font-weight: 700;
  }

  &.green {
    color: $success;
  }

  &.orange {
    color: #f97316;
  }

  &.red {
    color: $error;
  }
}

.mb-2 {
  @include mb-2;
}

.mt-6 {
  margin-top: 48rpx;
}

.hot-rank-list {
  @include flex;
  @include flex-col;
  gap: 14rpx;
}

.hot-rank-item {
  @include flex;
  @include items-center;
  gap: 16rpx;
  margin-bottom: 14rpx;
}

.rank-number {
  font-size: 36rpx;
  font-weight: bold;
  width: 60rpx;
  text-align: center;

  &.red {
    @include text-primary;
  }

  &.orange {
    color: #ffa726;
  }

  &.cyan {
    color: #00f2ea;
  }
}

.rank-img {
  width: 76rpx;
  height: 76rpx;
  border-radius: 20rpx;
  object-fit: cover;
  border: 4rpx solid rgba(0, 242, 234, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 242, 234, 0.13);
}

.rank-name {
  flex: 1;
  font-size: 32rpx;
}

.rank-count {
  font-size: 30rpx;
  color: #4b5563;
}


/* 统计页面特定样式 */
.stats-card {
  @include modern-card;
  @include card-primary;
  @include p-4;
  @include mb-4;
}

.chart-container {
  @include modern-card;
  @include p-4;
  height: 400rpx;
}