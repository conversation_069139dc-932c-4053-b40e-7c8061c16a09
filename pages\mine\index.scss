/* 现代化设计 */
@import "../../styles/miniprogram-design.scss";

/* 我的页面 - Tailwind CSS 风格 */

.container {
  @include page-container;
  @include page-container-safe;
}

.mine-user-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @include rounded-lg;
  @include shadow-md;
  @include mb-4;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;
  @include flex;
  @include items-center;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    @include modern-btn; @include btn-primary;
    @extend .rounded-lg 24rpx 0 0;
  }
}

.mine-user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  @include modern-btn; @include btn-primary;
  @include flex;
  @include items-center;
  @include justify-center;
  margin-right: 24rpx;
  @include shadow-md;

  image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.mine-user-info {
  flex: 1;
}

.mine-user-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8rpx;
}

.mine-user-phone {
  color: #d1d5db;
  font-size: 26rpx;
}

.mine-action-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @include rounded-lg;
  @include p-4;
  border: 2rpx solid #4b5563;
}

.mine-links-section {
  @include flex;
  @include justify-between;
  gap: 16rpx;
  @include mb-4;
}

.mine-link-btn {
  @include flex;
  @include items-center;
  @include justify-center;
  width: 48%;
  padding: 20rpx 0;
  @include rounded-md;
  text-align: center;
  font-weight: 600;
  font-size: 26rpx;
  @include shadow-md;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &.message {
    @include modern-btn; @include btn-primary;
    color: #111827;
  }

  &.notice {
    background-@include text-primary;
    color: #111827;
  }
}

.mine-action-btn {
  @include modern-btn; @include btn-primary;
  color: #111827;
  border: none;
  @include rounded-md;
  padding: 20rpx 0;
  font-size: 28rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 16rpx;
  @include shadow-md;
  @include flex;
  @include items-center;
  @include justify-center;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}


/* 我的页面特定样式 */
.mine-user-card {
  @include modern-card;
  @include card-primary;
  @include flex;
  @include items-center;
  @include gap-3;
  @include p-4;
  @include mb-4;
}

.mine-action-card {
  @include modern-card;
  @include p-4;
}

.mine-link-btn {
  @include modern-btn;
  @include btn-secondary;
  @include flex-1;
}

.mine-action-btn {
  @include modern-btn;
  @include btn-gradient;
  @include btn-full;
}