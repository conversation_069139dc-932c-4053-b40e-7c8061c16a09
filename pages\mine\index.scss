.container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 40rpx;
  min-height: 100vh;
  background: #121212;
}

.mine-user-card {
  background: linear-gradient(120deg, #1f1f1f 60%, #2a2a2a 100%);
  border-radius: 44rpx;
  box-shadow:
    0 12rpx 64rpx rgba(0, 0, 0, 0.67),
    0 3rpx 0 #232323 inset;
  margin-bottom: 56rpx;
  padding: 56rpx 40rpx;
  border: 3rpx solid #232323;
  backdrop-filter: blur(8rpx);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;

  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
    border-radius: 44rpx 44rpx 0 0;
    opacity: 0.8;
  }
}

.mine-user-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #fe2c55 60%, #00f2ea 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 36rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);

  image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.mine-user-info {
  flex: 1;
}

.mine-user-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 8rpx;
}

.mine-user-phone {
  color: #e6e6e6;
  font-size: 30rpx;
}

.mine-action-card {
  background: linear-gradient(135deg, rgba(35, 39, 47, 0.92) 60%, rgba(24, 26, 32, 0.88) 100%);
  border-radius: 44rpx;
  padding: 40rpx;
  border: 3rpx solid #232323;
}

.mine-links-section {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  margin-bottom: 48rpx;
}

.mine-link-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48% !important;
  padding: 28rpx 0;
  border-radius: 28rpx;
  text-align: center;
  font-weight: 600;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }

  &.message {
    background: #00f2ea;
    color: #181a20;
  }

  &.notice {
    background: #fe2c55;
    color: #fff;
  }
}

.mine-action-btn {
  background: linear-gradient(90deg, #fe2c55 60%, #00f2ea 100%);
  color: #fff !important;
  border: none;
  border-radius: 28rpx;
  padding: 28rpx 0;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100% !important;

  // &:active {
  //   background: linear-gradient(90deg, #fe2c55 80%, #00f2ea 100%);
  // }
}
