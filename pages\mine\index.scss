/* 我的页面 - 小程序兼容设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
}

.mine-user-card {
  @include modern-card;
  @include card-primary;
  @include flex;
  @include items-center;
  @include gap-3;
  @include mb-4;
  padding: 40rpx 32rpx;
}

.mine-user-avatar {
  width: 80rpx;
  height: 80rpx;
  @include rounded-full;
  @include shadow-sm;
  flex-shrink: 0;

  image {
    width: 100%;
    height: 100%;
    @include rounded-full;
  }
}

.mine-user-info {
  @include flex-1;
}

.mine-user-name {
  @include text-lg;
  @include font-bold;
  @include text-gray-900;
  @include mb-2;
}

.mine-user-phone {
  @include text-sm;
  @include text-gray-600;
}

.mine-action-card {
  @include modern-card;
  @include p-4;
}

.mine-links-section {
  @include flex;
  @include justify-between;
  @include gap-2;
  @include mb-4;
}

.mine-link-btn {
  @include modern-btn;
  @include btn-secondary;
  @include flex-1;
  @include text-sm;

  &.message {
    @include btn-primary;
  }

  &.notice {
    @include btn-secondary;
  }
}

.mine-action-btn {
  @include modern-btn;
  @include btn-primary;
  @include btn-full;
  @include text-base;
  @include font-semibold;
  margin-top: 16rpx;
}
