/* 我的页面 - Tailwind CSS 风格 */

.container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 32rpx;
  min-height: 100vh;
  background-color: #111827;
}

.mine-user-card {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 24rpx;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  margin-bottom: 32rpx;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #ec4899, #3b82f6);
    border-radius: 24rpx 24rpx 0 0;
  }
}

.mine-user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ec4899, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.mine-user-info {
  flex: 1;
}

.mine-user-name {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 8rpx;
}

.mine-user-phone {
  color: #d1d5db;
  font-size: 26rpx;
}

.mine-action-card {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 2rpx solid #4b5563;
}

.mine-links-section {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.mine-link-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48%;
  padding: 20rpx 0;
  border-radius: 16rpx;
  text-align: center;
  font-weight: 600;
  font-size: 26rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &.message {
    background-color: #3b82f6;
    color: white;
  }

  &.notice {
    background-color: #ec4899;
    color: white;
  }
}

.mine-action-btn {
  background: linear-gradient(135deg, #ec4899, #3b82f6);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 16rpx;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}
