/* 现代化设计 */
@import "../../styles/modern-design.scss";

/* 我的页面 - Tailwind CSS 风格 */

.container {
  @extend .page-container;
  @extend .page-container-safe;
}

.mine-user-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @extend .rounded-lg;
  @extend .shadow-md;
  @extend .mb-4;
  padding: 48rpx 32rpx;
  border: 2rpx solid #4b5563;
  position: relative;
  overflow: hidden;
  @extend .flex;
  @extend .items-center;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    @extend .modern-btn; @extend .btn-primary;
    @extend .rounded-lg 24rpx 0 0;
  }
}

.mine-user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  @extend .modern-btn; @extend .btn-primary;
  @extend .flex;
  @extend .items-center;
  @extend .justify-center;
  margin-right: 24rpx;
  @extend .shadow-md;

  image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.mine-user-info {
  flex: 1;
}

.mine-user-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8rpx;
}

.mine-user-phone {
  color: #d1d5db;
  font-size: 26rpx;
}

.mine-action-card {
  background: linear-gradient(135deg, white, #f3f4f6);
  @extend .rounded-lg;
  @extend .p-4;
  border: 2rpx solid #4b5563;
}

.mine-links-section {
  @extend .flex;
  @extend .justify-between;
  gap: 16rpx;
  @extend .mb-4;
}

.mine-link-btn {
  @extend .flex;
  @extend .items-center;
  @extend .justify-center;
  width: 48%;
  padding: 20rpx 0;
  @extend .rounded-md;
  text-align: center;
  font-weight: 600;
  font-size: 26rpx;
  @extend .shadow-md;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &.message {
    @extend .modern-btn; @extend .btn-primary;
    color: #111827;
  }

  &.notice {
    background-@extend .text-primary;
    color: #111827;
  }
}

.mine-action-btn {
  @extend .modern-btn; @extend .btn-primary;
  color: #111827;
  border: none;
  @extend .rounded-md;
  padding: 20rpx 0;
  font-size: 28rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 16rpx;
  @extend .shadow-md;
  @extend .flex;
  @extend .items-center;
  @extend .justify-center;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}


/* 我的页面特定样式 */
.mine-user-card {
  @extend .modern-card;
  @extend .card-primary;
  @extend .flex;
  @extend .items-center;
  @extend .gap-3;
  @extend .p-4;
  @extend .mb-4;
}

.mine-action-card {
  @extend .modern-card;
  @extend .p-4;
}

.mine-link-btn {
  @extend .modern-btn;
  @extend .btn-secondary;
  @extend .flex-1;
}

.mine-action-btn {
  @extend .modern-btn;
  @extend .btn-gradient;
  @extend .btn-full;
}