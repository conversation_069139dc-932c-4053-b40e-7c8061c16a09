/* 我的页面 - 小程序兼容设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
}

.mine-user-card {
  @include modern-card;
  @include card-primary;
  @include flex;
  @include items-center;
  @include gap-3;
  @include mb-4;
  padding: 40rpx 32rpx;
}

.mine-user-avatar {
  width: 80rpx;
  height: 80rpx;
  @include rounded-full;
  @include shadow-sm;
  flex-shrink: 0;

  image {
    width: 100%;
    height: 100%;
    @include rounded-full;
  }
}

.mine-user-info {
  @include flex-1;
  @include flex;
  @include flex-col;
  @include gap-2;
}

.mine-user-name {
  @include text-xl;
  @include font-bold;
  @include text-gray-900;
}

.mine-user-phone,
.mine-user-role,
.mine-user-id {
  @include text-xs;
  @include text-gray-600;
  @include flex;
  @include items-center;
}

.mine-user-badge {
  @include flex;
  @include items-center;
  @include justify-center;
  width: 48rpx;
  height: 48rpx;
  @include rounded-full;
  background: rgba($warning, 0.1);
}

.mine-action-card {
  @include modern-card;
  @include p-4;
}

.mine-links-section {
  @include flex;
  @include justify-between;
  @include gap-2;
  @include mb-4;
}

.mine-link-btn {
  @include modern-btn;
  @include btn-secondary;
  @include flex-1;
  @include text-sm;

  &.message {
    @include btn-primary;
  }

  &.notice {
    @include btn-secondary;
  }
}

.mine-action-btn {
  @include modern-btn;
  @include btn-full;
  @include text-base;
  @include font-semibold;
  @include flex;
  @include items-center;
  @include justify-center;
  @include gap-3;
  height: 96rpx;

  &.logout-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    @include text-white;
    @include shadow-lg;

    &:active {
      transform: scale(0.98);
      @include shadow-md;
    }

    .van-icon {
      @include text-white;
    }
  }
}
