const app = getApp();
const {messageApi} = require('../../services/api');

Page({
  data: {
    messages: [],
    messageInput: '',
    userInfo: {},
    loading: true
  },

  onLoad() {
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo') || {};
    this.setData({userInfo});

    // 加载消息数据
    this.loadMessages();
  },

  onShow() {
    // 页面显示时刷新消息
    this.loadMessages();
  },

  // 加载消息数据
  async loadMessages() {
    try {
      wx.showLoading({title: '加载中...'});

      const result = await messageApi.getMessages();

      if (result.code === 200) {
        // 处理不同的数据格式
        let messageData = result.data;

        // 确保数据是数组格式
        if (!Array.isArray(messageData)) {
          messageData =
            messageData.messages || messageData.list || messageData.data || [];
        }

        // 如果最终还不是数组，设为空数组
        if (!Array.isArray(messageData)) {
          messageData = [];
        }

        const formattedMessages = messageData.map(msg => {
          // 根据用户ID设置不同的样式类
          const userType = msg.userId % 2 === 0 ? 'blue' : 'pink';

          return {
            id: msg.id,
            userName: msg.user?.name || '用户',
            content: msg.content,
            time: this.formatTime(msg.createdAt),
            userType,
            read: msg.read
          };
        });

        this.setData({
          messages: formattedMessages,
          loading: false
        });
      } else {
        throw new Error(result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载消息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      this.setData({loading: false});
    } finally {
      wx.hideLoading();
    }
  },

  // 格式化时间
  formatTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    // 如果是今天
    if (diff < 24 * 60 * 60 * 1000) {
      return `${String(date.getHours()).padStart(2, '0')}:${String(
        date.getMinutes()
      ).padStart(2, '0')}`;
    }

    // 如果是其他日期
    return `${date.getMonth() + 1}-${date.getDate()} ${String(
      date.getHours()
    ).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  },

  // 留言输入
  onMessageInput(e) {
    this.setData({
      messageInput: e.detail.value
    });
  },

  // 添加留言
  async addMessage() {
    const {messageInput, userInfo} = this.data;

    if (!messageInput.trim()) {
      wx.showToast({
        title: '请输入留言内容',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({title: '发送中...'});

      const result = await messageApi.createMessage({
        content: messageInput.trim()
      });

      if (result.code === 200) {
        // 清空输入框
        this.setData({
          messageInput: ''
        });

        // 重新加载消息列表
        await this.loadMessages();

        // 提示用户
        wx.showToast({
          title: '留言已发送',
          icon: 'success',
          duration: 1500
        });
      } else {
        throw new Error(result.message || '发送失败');
      }
    } catch (error) {
      console.error('发送留言失败:', error);
      wx.showToast({
        title: error.message || '发送失败，请重试',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  }
});
