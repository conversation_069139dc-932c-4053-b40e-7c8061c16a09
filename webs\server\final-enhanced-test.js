require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function finalEnhancedTest() {
  console.log('🚀 最终增强功能测试：权限系统和智能推送...\n');

  try {
    // 1. 注册两个用户
    console.log('1️⃣ 注册测试用户...');
    
    const user1Phone = `139${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`;
    const user2Phone = `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`;
    
    const user1Data = {
      name: '张三',
      phone: user1Phone,
      password: '123456'
    };
    
    const user2Data = {
      name: '李四',
      phone: user2Phone,
      password: '123456'
    };

    const user1Res = await axios.post(`${BASE_URL}/auth/register`, user1Data);
    const user2Res = await axios.post(`${BASE_URL}/auth/register`, user2Data);
    
    const user1Token = user1Res.data.data.token;
    const user2Token = user2Res.data.data.token;
    
    console.log('✅ 用户注册成功');
    console.log('   用户1:', user1Res.data.data.user.name);
    console.log('   用户2:', user2Res.data.data.user.name);

    // 2. 测试智能推送 - 创建菜品
    console.log('\n2️⃣ 测试菜品创建智能推送...');
    
    const dishData = {
      name: `智能推送测试菜_${Date.now()}`,
      description: '这是一道用于测试智能推送功能的菜品',
      category: '热菜',
      image: 'https://images.pexels.com/photos/2097090/pexels-photo-2097090.jpeg'
    };

    const dishRes = await axios.post(`${BASE_URL}/dishes`, dishData, {
      headers: { Authorization: `Bearer ${user1Token}` }
    });

    if (dishRes.data.code === 201) {
      console.log('✅ 菜品创建成功，智能推送已触发');
      console.log('   菜品名称:', dishRes.data.data.name);
    }

    // 3. 测试通知获取
    console.log('\n3️⃣ 测试通知获取...');
    
    // 等待一下让通知创建完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const notificationsRes = await axios.get(`${BASE_URL}/notifications`, {
      headers: { Authorization: `Bearer ${user2Token}` }
    });

    if (notificationsRes.data.code === 200) {
      const notifications = notificationsRes.data.data.notifications;
      console.log('✅ 通知获取成功');
      console.log(`   用户2收到 ${notifications.length} 条通知`);
      console.log('   未读数量:', notificationsRes.data.data.unreadCount);
      
      if (notifications.length > 0) {
        console.log('   最新通知:', notifications[0].content);
        console.log('   通知类型:', notifications[0].type);
      }
    }

    // 4. 测试订单创建智能推送
    console.log('\n4️⃣ 测试订单创建智能推送...');
    
    const orderData = {
      items: [
        { dishName: '智能推送测试菜', count: 2, price: 25.00 },
        { dishName: '红烧肉', count: 1, price: 30.00 }
      ],
      remark: '测试智能推送功能'
    };

    const orderRes = await axios.post(`${BASE_URL}/orders`, orderData, {
      headers: { Authorization: `Bearer ${user2Token}` }
    });

    if (orderRes.data.code === 201) {
      console.log('✅ 订单创建成功，智能推送已触发');
      console.log('   订单ID:', orderRes.data.data.id);
    }

    // 5. 测试权限控制 - 尝试发送家庭通知（应该失败）
    console.log('\n5️⃣ 测试权限控制...');
    
    try {
      await axios.post(`${BASE_URL}/notifications/family`, {
        content: '这是一条家庭通知测试'
      }, {
        headers: { Authorization: `Bearer ${user1Token}` }
      });
      console.log('❌ 权限控制失败：普通用户不应该能发送家庭通知');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ 权限控制正常：普通用户无法发送家庭通知');
      } else {
        console.log('⚠️ 权限测试出现意外错误:', error.response?.data?.message);
      }
    }

    // 6. 测试通知标记为已读
    console.log('\n6️⃣ 测试通知标记功能...');
    
    const unreadCountBefore = await axios.get(`${BASE_URL}/notifications/unread-count`, {
      headers: { Authorization: `Bearer ${user2Token}` }
    });
    
    console.log('   标记前未读数量:', unreadCountBefore.data.data.count);
    
    // 标记所有通知为已读
    const markAllRes = await axios.put(`${BASE_URL}/notifications/read-all`, {}, {
      headers: { Authorization: `Bearer ${user2Token}` }
    });
    
    if (markAllRes.data.code === 200) {
      console.log('✅ 所有通知已标记为已读');
      console.log('   标记数量:', markAllRes.data.data.count);
    }
    
    const unreadCountAfter = await axios.get(`${BASE_URL}/notifications/unread-count`, {
      headers: { Authorization: `Bearer ${user2Token}` }
    });
    
    console.log('   标记后未读数量:', unreadCountAfter.data.data.count);

    // 7. 测试通知类型过滤
    console.log('\n7️⃣ 测试通知类型过滤...');
    
    const filteredNotifications = await axios.get(`${BASE_URL}/notifications?type=new_dish`, {
      headers: { Authorization: `Bearer ${user2Token}` }
    });
    
    if (filteredNotifications.data.code === 200) {
      const dishNotifications = filteredNotifications.data.data.notifications;
      console.log('✅ 通知类型过滤成功');
      console.log(`   新菜品通知数量: ${dishNotifications.length}`);
    }

    console.log('\n🎉 增强功能测试完成！');
    console.log('\n📝 测试总结:');
    console.log('✅ 用户注册智能推送 - 正常');
    console.log('✅ 菜品创建智能推送 - 正常');
    console.log('✅ 订单创建智能推送 - 正常');
    console.log('✅ 权限控制系统 - 正常');
    console.log('✅ 通知管理功能 - 正常');
    console.log('✅ 通知类型过滤 - 正常');
    console.log('\n🚀 智能推送和权限系统完全正常！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('状态码:', error.response.status);
    }
  }
}

// 运行测试
finalEnhancedTest();
