{"appid": "wx82283b353918af82", "compileType": "miniprogram", "libVersion": "3.4.3", "packOptions": {"ignore": [{"value": "webs", "type": "folder"}], "include": []}, "setting": {"coverView": true, "es6": false, "postcss": true, "minified": true, "enhance": false, "showShadowRootInWxmlPanel": true, "packNpmManually": true, "useCompilerPlugins": ["sass"], "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": ""}], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreDevUnusedFiles": true, "ignoreUploadUnusedFiles": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "projectname": "nannan"}