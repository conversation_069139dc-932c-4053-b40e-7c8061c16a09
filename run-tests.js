#!/usr/bin/env node

/**
 * 小程序测试运行脚本
 * 运行所有测试并生成报告
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始运行小程序测试...\n');

// 检查测试环境
function checkTestEnvironment() {
  console.log('📋 检查测试环境...');
  
  try {
    // 检查 Jest 是否安装
    execSync('npx jest --version', { stdio: 'pipe' });
    console.log('✅ Jest 已安装');
    
    // 检查测试文件是否存在
    const testDir = path.join(__dirname, 'tests');
    if (!fs.existsSync(testDir)) {
      throw new Error('测试目录不存在');
    }
    console.log('✅ 测试目录存在');
    
    // 检查测试配置文件
    const jestConfig = path.join(__dirname, 'jest.config.js');
    if (!fs.existsSync(jestConfig)) {
      throw new Error('Jest 配置文件不存在');
    }
    console.log('✅ Jest 配置文件存在');
    
  } catch (error) {
    console.error('❌ 测试环境检查失败:', error.message);
    process.exit(1);
  }
  
  console.log('');
}

// 运行测试
function runTests() {
  console.log('🧪 运行测试套件...\n');
  
  try {
    // 运行所有测试
    const result = execSync('npm test', { 
      stdio: 'inherit',
      encoding: 'utf8'
    });
    
    console.log('\n✅ 所有测试运行完成');
    
  } catch (error) {
    console.error('\n❌ 测试运行失败');
    console.error('错误信息:', error.message);
    
    // 显示测试失败的详细信息
    if (error.stdout) {
      console.log('\n测试输出:');
      console.log(error.stdout);
    }
    
    if (error.stderr) {
      console.log('\n错误输出:');
      console.log(error.stderr);
    }
    
    process.exit(1);
  }
}

// 生成测试报告
function generateTestReport() {
  console.log('\n📊 生成测试覆盖率报告...');
  
  try {
    execSync('npm run test:coverage', { 
      stdio: 'inherit',
      encoding: 'utf8'
    });
    
    console.log('✅ 测试覆盖率报告已生成');
    console.log('📁 报告位置: ./coverage/lcov-report/index.html');
    
  } catch (error) {
    console.warn('⚠️  生成覆盖率报告失败:', error.message);
  }
}

// 显示测试总结
function showTestSummary() {
  console.log('\n📋 测试总结:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const testFiles = [
    'tests/pages/login.test.js',
    'tests/pages/order.test.js', 
    'tests/pages/today_order.test.js',
    'tests/components/refresh-list.test.js',
    'tests/integration/app-flow.test.js'
  ];
  
  console.log('🧪 测试文件:');
  testFiles.forEach(file => {
    const exists = fs.existsSync(path.join(__dirname, file));
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  });
  
  console.log('\n🎯 测试覆盖范围:');
  console.log('  ✅ 用户登录功能');
  console.log('  ✅ 点菜功能');
  console.log('  ✅ 订单提交功能');
  console.log('  ✅ 用户选择功能');
  console.log('  ✅ 刷新列表组件');
  console.log('  ✅ 完整业务流程');
  
  console.log('\n🔧 功能特性:');
  console.log('  ✅ Tailwind CSS 样式系统');
  console.log('  ✅ 用户选择功能');
  console.log('  ✅ 上拉刷新下拉加载');
  console.log('  ✅ 组件化设计');
  console.log('  ✅ 错误处理');
  console.log('  ✅ 数据验证');
  
  console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('🎉 小程序测试完成！');
}

// 主函数
function main() {
  try {
    checkTestEnvironment();
    runTests();
    generateTestReport();
    showTestSummary();
    
  } catch (error) {
    console.error('\n💥 测试运行过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkTestEnvironment,
  runTests,
  generateTestReport,
  showTestSummary
};
