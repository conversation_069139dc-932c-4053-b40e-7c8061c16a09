const {menuApi, orderApi} = require('../../services/api');

Page({
  data: {
    loading: true,
    weeklyFavorite: '加载中...',
    monthlyTotal: 0,
    healthTip: '正在分析您的饮食习惯...',
    hotRank: [],
    statistics: {
      todayOrders: 0,
      totalDishes: 0,
      activeUsers: 0,
      monthlyVisits: 0
    },
    chartData: {
      categories: ['热菜', '凉菜', '汤品', '主食', '甜品'],
      data: [0, 0, 0, 0, 0]
    }
  },

  onLoad() {
    // 加载统计数据
    this.loadAllStatistics();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAllStatistics();
  },

  // 加载所有统计数据
  async loadAllStatistics() {
    try {
      await Promise.all([
        this.getStatisticsData(),
        this.getOrderStatistics(),
        this.getHotRankData()
      ]);

      this.setData({loading: false});
    } catch (error) {
      console.error('加载统计数据失败:', error);
      this.setData({loading: false});
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    }
  },

  // 获取基础统计数据
  async getStatisticsData() {
    try {
      const result = await menuApi.getStatistics();

      if (result.code === 200) {
        const stats = result.data;

        this.setData({
          statistics: {
            todayOrders: stats.todayOrders || 0,
            totalDishes: stats.totalDishes || 0,
            activeUsers: stats.activeUsers || 0,
            monthlyVisits: stats.monthlyVisits || 0
          }
        });

        // 生成健康建议
        this.generateHealthTip(stats);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  },

  // 获取订单统计数据
  async getOrderStatistics() {
    try {
      const result = await orderApi.getOrders();

      if (result.code === 200) {
        let orders = result.data;

        // 确保 orders 是数组
        if (!Array.isArray(orders)) {
          console.warn('订单数据不是数组格式:', orders);
          orders = orders.list || orders.data || [];
        }

        // 计算本月订单总数
        const currentMonth = new Date().getMonth();
        const monthlyOrders = orders.filter(order => {
          const orderMonth = new Date(order.createdAt).getMonth();
          return orderMonth === currentMonth;
        });

        this.setData({
          monthlyTotal: monthlyOrders.length
        });

        // 分析分类数据
        this.analyzeOrderCategories(orders);
      }
    } catch (error) {
      console.error('获取订单统计失败:', error);
      // 设置默认值
      this.setData({
        monthlyTotal: 0
      });
    }
  },

  // 获取热门菜品排行
  async getHotRankData() {
    try {
      // 从本地存储获取历史菜单数据进行分析
      const historyMenus = wx.getStorageSync('historyMenus') || [];
      const dishCount = {};

      // 统计菜品出现次数
      historyMenus.forEach(menu => {
        if (menu.dishes && Array.isArray(menu.dishes)) {
          menu.dishes.forEach(dish => {
            const key = dish.name || dish.dishName;
            if (key) {
              dishCount[key] = (dishCount[key] || 0) + (dish.count || 1);
            }
          });
        }
      });

      // 转换为排行榜格式并排序
      const hotRank = Object.entries(dishCount)
        .map(([name, count]) => ({
          name,
          count,
          img: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg'
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5); // 取前5名

      // 设置本周最爱
      const weeklyFavorite =
        hotRank.length > 0
          ? `${hotRank[0].name}（${hotRank[0].count}次）`
          : '暂无数据';

      this.setData({
        hotRank,
        weeklyFavorite
      });
    } catch (error) {
      console.error('获取热门菜品失败:', error);
    }
  },

  // 分析订单分类数据
  analyzeOrderCategories(orders) {
    const categoryCount = {
      热菜: 0,
      凉菜: 0,
      汤品: 0,
      主食: 0,
      甜品: 0
    };

    // 这里可以根据实际订单数据分析分类
    // 暂时使用模拟数据
    const chartData = {
      categories: ['热菜', '凉菜', '汤品', '主食', '甜品'],
      data: [45, 20, 15, 15, 5] // 百分比数据
    };

    this.setData({chartData});
  },

  // 生成健康建议
  generateHealthTip(stats) {
    const tips = [
      '饮食搭配均衡，营养更全面！',
      '建议多吃蔬菜，保持健康饮食！',
      '荤素搭配，营养更丰富！',
      '适量饮汤，有助消化吸收！',
      '甜品适量，保持身材健康！'
    ];

    const randomTip = tips[Math.floor(Math.random() * tips.length)];

    this.setData({
      healthTip: randomTip
    });
  }
});
