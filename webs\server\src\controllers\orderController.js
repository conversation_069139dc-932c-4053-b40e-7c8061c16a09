const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');
const notificationService = require('../services/notificationService');

/**
 * 获取订单列表（支持分页和搜索）
 * @route GET /api/orders
 */
const getOrders = async (req, res) => {
  try {
    const {
      page = 1,
      size = 10,
      userId,
      status,
      userName,
      startDate,
      endDate
    } = req.query;

    // 构建查询条件
    const where = {};

    if (userId) where.userId = userId;
    if (status) where.status = status;

    if (userName) {
      where.user = {
        name: {
          contains: userName,
          mode: 'insensitive'
        }
      };
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.createdAt.lte = endDateTime;
      }
    }

    // 计算分页
    const skip = (parseInt(page) - 1) * parseInt(size);
    const take = parseInt(size);

    // 获取总数
    const total = await prisma.order.count({where});

    // 获取订单列表
    const orders = await prisma.order.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true,
            phone: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take
    });

    return success(res, {
      list: orders,
      total,
      page: parseInt(page),
      size: parseInt(size),
      totalPages: Math.ceil(total / parseInt(size))
    });
  } catch (err) {
    console.error('Get orders error:', err);
    return error(res, 'Failed to get orders', 500);
  }
};

/**
 * 获取今日订单
 * @route GET /api/orders/today
 */
const getTodayOrders = async (req, res) => {
  try {
    // 获取今天的日期（不包含时间）
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 查找今日订单
    const todayOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return success(res, todayOrders);
  } catch (err) {
    console.error('Get today orders error:', err);
    return error(res, 'Failed to get today orders', 500);
  }
};

/**
 * 获取指定订单
 * @route GET /api/orders/:id
 */
const getOrderById = async (req, res) => {
  try {
    const {id} = req.params;

    const order = await prisma.order.findUnique({
      where: {id},
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    if (!order) {
      return error(res, 'Order not found', 404);
    }

    return success(res, order);
  } catch (err) {
    console.error('Get order error:', err);
    return error(res, 'Failed to get order', 500);
  }
};

/**
 * 创建订单
 * @route POST /api/orders
 */
const createOrder = async (req, res) => {
  try {
    const {items, remark, diningTime} = req.body;
    const userId = req.user.id;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return error(res, 'Order items are required', 400);
    }

    // 创建订单
    const order = await prisma.order.create({
      data: {
        userId,
        items: JSON.stringify(items),
        remark,
        diningTime: diningTime ? new Date(diningTime) : null,
        status: 'pending'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    // 发送智能推送通知
    try {
      // 解析订单项以便通知
      const orderWithItems = {
        ...order,
        items: JSON.parse(order.items)
      };
      await notificationService.notifyNewOrder(orderWithItems, req.user);
    } catch (notifyError) {
      console.error('Failed to send notification:', notifyError);
      // 不影响主要功能，只记录错误
    }

    return success(res, order, 'Order created successfully', 201);
  } catch (err) {
    console.error('Create order error:', err);
    return error(res, 'Failed to create order', 500);
  }
};

/**
 * 更新订单
 * @route PUT /api/orders/:id
 */
const updateOrder = async (req, res) => {
  try {
    const {id} = req.params;
    const {items, remark, diningTime, status} = req.body;

    // 检查订单是否存在
    const existingOrder = await prisma.order.findUnique({
      where: {id}
    });

    if (!existingOrder) {
      return error(res, 'Order not found', 404);
    }

    // 准备更新数据
    const updateData = {};

    if (items && Array.isArray(items)) updateData.items = JSON.stringify(items);
    if (remark !== undefined) updateData.remark = remark;
    if (diningTime) updateData.diningTime = new Date(diningTime);
    if (status) updateData.status = status;

    // 更新订单
    const updatedOrder = await prisma.order.update({
      where: {id},
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    return success(res, updatedOrder, 'Order updated successfully');
  } catch (err) {
    console.error('Update order error:', err);
    return error(res, 'Failed to update order', 500);
  }
};

/**
 * 删除订单
 * @route DELETE /api/orders/:id
 */
const deleteOrder = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查订单是否存在
    const existingOrder = await prisma.order.findUnique({
      where: {id}
    });

    if (!existingOrder) {
      return error(res, 'Order not found', 404);
    }

    // 检查权限（只有管理员或订单所有者可以删除）
    if (req.user.role !== 'admin' && req.user.id !== existingOrder.userId) {
      return error(res, 'Permission denied', 403);
    }

    // 删除订单
    await prisma.order.delete({
      where: {id}
    });

    return success(res, null, 'Order deleted successfully');
  } catch (err) {
    console.error('Delete order error:', err);
    return error(res, 'Failed to delete order', 500);
  }
};

/**
 * 获取订单统计信息
 * @route GET /api/orders/statistics
 */
const getOrderStatistics = async (req, res) => {
  try {
    // 总订单数
    const totalOrders = await prisma.order.count();

    // 今日订单数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayOrders = await prisma.order.count({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    // 按状态统计
    const statusStats = await prisma.order.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    });

    // 最近7天订单趋势
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);

      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      const count = await prisma.order.count({
        where: {
          createdAt: {
            gte: date,
            lt: nextDate
          }
        }
      });

      last7Days.push({
        date: date.toISOString().split('T')[0],
        count
      });
    }

    return success(res, {
      totalOrders,
      todayOrders,
      statusStats: statusStats.map(stat => ({
        status: stat.status,
        count: stat._count.status
      })),
      last7Days
    });
  } catch (err) {
    console.error('Get order statistics error:', err);
    return error(res, 'Failed to get order statistics', 500);
  }
};

/**
 * 批量更新订单状态
 * @route POST /api/orders/batch-status
 */
const batchUpdateStatus = async (req, res) => {
  try {
    const {orderIds, status} = req.body;

    if (!orderIds || !Array.isArray(orderIds) || !status) {
      return error(res, 'Order IDs and status are required', 400);
    }

    const validStatuses = [
      'pending',
      'confirmed',
      'preparing',
      'ready',
      'completed',
      'cancelled'
    ];
    if (!validStatuses.includes(status)) {
      return error(res, 'Invalid status', 400);
    }

    const result = await prisma.order.updateMany({
      where: {id: {in: orderIds}},
      data: {status}
    });

    return success(res, result, `Batch status update completed successfully`);
  } catch (err) {
    console.error('Batch update status error:', err);
    return error(res, 'Failed to batch update status', 500);
  }
};

module.exports = {
  getOrders,
  getTodayOrders,
  getOrderById,
  createOrder,
  updateOrder,
  deleteOrder,
  getOrderStatistics,
  batchUpdateStatus
};
