<template>
  <div class="notification-list">
    <CustomTable
      title="系统通知"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @current-change="handleCurrentChange"
    >
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          发布通知
        </el-button>
      </template>
      
      <template #actions="{ row }">
        <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
        <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
      </template>
    </CustomTable>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import { notificationApi } from '@/api/message'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'NotificationList',
  components: {
    CustomTable,
    Plus
  },
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    
    const pagination = reactive({
      page: 1,
      size: 10,
      total: 0
    })
    
    const columns = [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'title', label: '标题' },
      { prop: 'content', label: '内容', showOverflowTooltip: true },
      { prop: 'createdAt', label: '发布时间', formatter: (row) => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm') }
    ]
    
    const loadData = async () => {
      loading.value = true
      try {
        const response = await notificationApi.getNotifications({
          page: pagination.page,
          size: pagination.size
        })
        if (response.data) {
          tableData.value = response.data.list || response.data
          pagination.total = response.data.total || response.data.length
        }
      } catch (error) {
        console.error('加载通知失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      loadData()
    }
    
    const handleCreate = () => {
      ElMessage.info('发布通知功能开发中...')
    }
    
    const handleEdit = (row) => {
      ElMessage.info(`编辑通知: ${row.title}`)
    }
    
    const handleDelete = async (row) => {
      try {
        await notificationApi.deleteNotification(row.id)
        ElMessage.success('删除成功')
        loadData()
      } catch (error) {
        console.error('删除通知失败:', error)
        ElMessage.error('删除失败')
      }
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      tableData,
      columns,
      pagination,
      handleCurrentChange,
      handleCreate,
      handleEdit,
      handleDelete
    }
  }
})
</script>
