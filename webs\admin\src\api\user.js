import {http} from '@/utils/request';

// 用户相关API
export const userApi = {
  // 获取用户列表
  getUsers: params => http.get('/users', {params}),

  // 获取用户详情
  getUserDetail: id => http.get(`/users/${id}`),

  // 创建用户
  createUser: data => http.post('/users', data),

  // 更新用户
  updateUser: (id, data) => http.put(`/users/${id}`, data),

  // 删除用户
  deleteUser: id => http.delete(`/users/${id}`),

  // 批量删除用户
  batchDeleteUsers: ids => http.delete('/users/batch', {data: {ids}}),

  // 重置密码
  resetPassword: (id, password) =>
    http.put(`/users/${id}/password`, {password}),

  // 获取用户统计
  getUserStatistics: () => http.get('/users/statistics'),

  // 获取家庭成员列表
  getFamilyMembers: () => http.get('/users/family')
};
