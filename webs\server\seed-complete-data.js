require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function seedCompleteData() {
  try {
    console.log('🌱 开始创建完整测试数据...');

    // 1. 创建用户
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    const users = await Promise.all([
      prisma.user.create({
        data: {
          name: '楠楠',
          phone: '13800138000',
          password: hashedPassword,
          role: 'admin',
          avatar: 'https://picsum.photos/200/200?random=1'
        }
      }),
      prisma.user.create({
        data: {
          name: '爸爸',
          phone: '13800138001',
          password: hashedPassword,
          role: 'family',
          avatar: 'https://picsum.photos/200/200?random=2'
        }
      }),
      prisma.user.create({
        data: {
          name: '妈妈',
          phone: '13800138002',
          password: hashedPassword,
          role: 'family',
          avatar: 'https://picsum.photos/200/200?random=3'
        }
      }),
      prisma.user.create({
        data: {
          name: '小明',
          phone: '13800138003',
          password: hashedPassword,
          role: 'family',
          avatar: 'https://picsum.photos/200/200?random=4'
        }
      })
    ]);
    console.log('✅ 创建用户:', users.map(u => u.name).join(', '));

    // 2. 创建分类
    const categories = await Promise.all([
      prisma.category.create({ data: { name: '热菜' } }),
      prisma.category.create({ data: { name: '凉菜' } }),
      prisma.category.create({ data: { name: '汤品' } }),
      prisma.category.create({ data: { name: '主食' } }),
      prisma.category.create({ data: { name: '甜品' } })
    ]);
    console.log('✅ 创建分类:', categories.map(c => c.name).join(', '));

    // 3. 创建菜品
    const dishes = await Promise.all([
      // 热菜
      prisma.dish.create({
        data: {
          name: '红烧肉',
          description: '经典红烧肉，肥而不腻，入口即化',
          image: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
          categoryId: categories[0].id
        }
      }),
      prisma.dish.create({
        data: {
          name: '宫保鸡丁',
          description: '川菜经典，麻辣鲜香，下饭神器',
          image: 'https://cdn.pixabay.com/photo/2015/04/08/13/13/food-712665_1280.jpg',
          categoryId: categories[0].id
        }
      }),
      prisma.dish.create({
        data: {
          name: '糖醋里脊',
          description: '酸甜可口，老少皆宜',
          image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/vegetables-1238251_1280.jpg',
          categoryId: categories[0].id
        }
      }),
      prisma.dish.create({
        data: {
          name: '鱼香肉丝',
          description: '四川名菜，鱼香味浓郁',
          image: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290815_1280.jpg',
          categoryId: categories[0].id
        }
      }),
      // 凉菜
      prisma.dish.create({
        data: {
          name: '凉拌黄瓜',
          description: '清爽解腻，夏日必备',
          image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/salad-1238248_1280.jpg',
          categoryId: categories[1].id
        }
      }),
      prisma.dish.create({
        data: {
          name: '蒜泥白肉',
          description: '四川凉菜，蒜香浓郁',
          image: 'https://cdn.pixabay.com/photo/2015/04/08/13/13/food-712665_1280.jpg',
          categoryId: categories[1].id
        }
      }),
      // 汤品
      prisma.dish.create({
        data: {
          name: '紫菜蛋花汤',
          description: '清淡营养，家常必备',
          image: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290815_1280.jpg',
          categoryId: categories[2].id
        }
      }),
      prisma.dish.create({
        data: {
          name: '冬瓜排骨汤',
          description: '清热解腻，营养丰富',
          image: 'https://cdn.pixabay.com/photo/2016/03/05/19/02/vegetables-1238251_1280.jpg',
          categoryId: categories[2].id
        }
      }),
      // 主食
      prisma.dish.create({
        data: {
          name: '蛋炒饭',
          description: '简单美味，粒粒分明',
          image: 'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
          categoryId: categories[3].id
        }
      }),
      prisma.dish.create({
        data: {
          name: '小笼包',
          description: '皮薄馅大，汁多味美',
          image: 'https://cdn.pixabay.com/photo/2015/04/08/13/13/food-712665_1280.jpg',
          categoryId: categories[3].id
        }
      })
    ]);
    console.log('✅ 创建菜品:', dishes.length, '道');

    // 4. 创建今日菜单
    const today = new Date();
    const todayMenu = await prisma.menu.create({
      data: {
        date: today,
        isToday: true,
        remark: '今日特色菜单，营养搭配均衡',
        items: {
          create: [
            { dishId: dishes[0].id, count: 8 }, // 红烧肉
            { dishId: dishes[1].id, count: 6 }, // 宫保鸡丁
            { dishId: dishes[4].id, count: 10 }, // 凉拌黄瓜
            { dishId: dishes[6].id, count: 12 }, // 紫菜蛋花汤
            { dishId: dishes[8].id, count: 15 } // 蛋炒饭
          ]
        }
      }
    });
    console.log('✅ 创建今日菜单:', todayMenu.id);

    // 5. 创建历史菜单
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const historyMenu = await prisma.menu.create({
      data: {
        date: yesterday,
        isToday: false,
        remark: '昨日菜单',
        items: {
          create: [
            { dishId: dishes[2].id, count: 5 }, // 糖醋里脊
            { dishId: dishes[3].id, count: 7 }, // 鱼香肉丝
            { dishId: dishes[5].id, count: 8 }, // 蒜泥白肉
            { dishId: dishes[7].id, count: 10 }, // 冬瓜排骨汤
            { dishId: dishes[9].id, count: 12 } // 小笼包
          ]
        }
      }
    });
    console.log('✅ 创建历史菜单:', historyMenu.id);

    console.log('\n🎉 基础数据创建完成！');
    
    // 验证数据
    const counts = await Promise.all([
      prisma.user.count(),
      prisma.category.count(),
      prisma.dish.count(),
      prisma.menu.count(),
      prisma.menuItem.count()
    ]);
    
    console.log('\n📊 数据统计:');
    console.log(`   用户: ${counts[0]} 条`);
    console.log(`   分类: ${counts[1]} 条`);
    console.log(`   菜品: ${counts[2]} 条`);
    console.log(`   菜单: ${counts[3]} 条`);
    console.log(`   菜单项: ${counts[4]} 条`);

    return { users, categories, dishes, todayMenu, historyMenu };

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

seedCompleteData();
