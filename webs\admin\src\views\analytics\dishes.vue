<template>
  <div class="dish-analytics">
    <!-- 菜品统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col
        :xs="24"
        :sm="12"
        :md="6"
        v-for="(stat, index) in dishStats"
        :key="index"
      >
        <StatsCard
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :type="stat.type"
          :trend="stat.trend"
          :description="stat.description"
          :animated="true"
        />
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 菜品销量排行 -->
      <el-col :xs="24" :lg="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>菜品销量排行</h3>
            <div class="chart-controls">
              <el-radio-group
                v-model="salesPeriod"
                size="small"
                @change="updateSalesChart"
              >
                <el-radio-button label="7d">近7天</el-radio-button>
                <el-radio-button label="30d">近30天</el-radio-button>
                <el-radio-button label="90d">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="salesChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 菜品收入分析 -->
      <el-col :xs="24" :lg="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>菜品收入分析</h3>
            <el-button size="small" @click="refreshRevenueChart">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div ref="revenueChartRef" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 分类分析和趋势 -->
    <el-row :gutter="20" class="analysis-section">
      <!-- 分类销量分布 -->
      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>分类销量分布</h3>
          </div>
          <div ref="categoryChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 菜品评分分析 -->
      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>菜品评分分析</h3>
          </div>
          <div ref="ratingChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 热门菜品列表 -->
      <el-col :xs="24" :lg="8">
        <div class="data-card">
          <div class="card-header">
            <h3>本周热门菜品</h3>
            <el-button size="small" @click="refreshHotDishes">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="hot-dishes-list">
            <div
              v-for="(dish, index) in hotDishes"
              :key="dish.id"
              class="dish-item"
            >
              <div class="dish-rank" :class="`rank-${index + 1}`">
                {{ index + 1 }}
              </div>
              <div class="dish-image">
                <el-image :src="dish.image" fit="cover" />
              </div>
              <div class="dish-info">
                <h4 class="dish-name">{{ dish.name }}</h4>
                <p class="dish-category">
                  {{ getCategoryText(dish.category) }}
                </p>
                <div class="dish-stats">
                  <span class="sales">销量: {{ dish.sales }}</span>
                  <span class="revenue">收入: ¥{{ dish.revenue }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <CustomTable
        title="菜品销售详情"
        :data="tableData"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        :show-search="true"
        :search-fields="searchFields"
        @search="handleSearch"
        @reset="handleReset"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
        <template #actions>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
        </template>

        <template #image="{ row }">
          <el-image
            :src="row.image"
            :preview-src-list="[row.image]"
            class="dish-image"
            fit="cover"
          />
        </template>

        <template #category="{ row }">
          <el-tag :type="getCategoryType(row.category)" size="small">
            {{ getCategoryText(row.category) }}
          </el-tag>
        </template>

        <template #sales="{ row }">
          <el-tag type="primary">{{ row.sales }}份</el-tag>
        </template>

        <template #revenue="{ row }">
          <span class="revenue-text">¥{{ row.revenue }}</span>
        </template>

        <template #rating="{ row }">
          <el-rate
            v-model="row.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { Refresh, Download, Bowl, TrendCharts, Star, Wallet } from '@element-plus/icons-vue'
import StatsCard from '@/components/StatsCard.vue'
import CustomTable from '@/components/CustomTable.vue'
import { dishApi } from '@/api/menu'
import { getCategoryText, getCategoryType } from '@/utils/common'

const salesChartRef = ref()
const revenueChartRef = ref()
const categoryChartRef = ref()
const ratingChartRef = ref()
const salesPeriod = ref('7d')
const tableLoading = ref(false)
const tableData = ref([])
const hotDishes = ref([])

let salesChart = null
let revenueChart = null
let categoryChart = null
let ratingChart = null

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  name: '',
  category: ''
})

// 菜品统计数据
const dishStats = reactive([
  {
    title: '菜品总数',
    value: 156,
    icon: Bowl,
    type: 'primary',
    trend: '+8',
    description: '较上月'
  },
  {
    title: '本周销量',
    value: 1240,
    icon: TrendCharts,
    type: 'success',
    trend: '+15%',
    description: '较上周'
  },
  {
    title: '平均评分',
    value: 4.6,
    icon: Star,
    type: 'warning',
    trend: '+0.2',
    description: '较上月'
  },
  {
    title: '总收入',
    value: '¥28,650',
    icon: Wallet,
    type: 'info',
    trend: '+12%',
    description: '较上月'
  }
])

// 表格列配置
const columns = [
  { prop: 'image', label: '图片', width: 80, slot: true },
  { prop: 'name', label: '菜品名称', minWidth: 120 },
  { prop: 'category', label: '分类', width: 100, slot: true },
  { prop: 'price', label: '价格', width: 80, formatter: (row) => `¥${row.price}` },
  { prop: 'sales', label: '销量', width: 100, slot: true },
  { prop: 'revenue', label: '收入', width: 120, slot: true },
  { prop: 'rating', label: '评分', width: 150, slot: true }
]

// 搜索字段配置
const searchFields = [
  { prop: 'name', label: '菜品名称', type: 'input' },
  { prop: 'category', label: '分类', type: 'select', options: [
    { label: '热菜', value: 'hot' },
    { label: '凉菜', value: 'cold' },
    { label: '汤品', value: 'soup' },
    { label: '主食', value: 'staple' },
    { label: '甜品', value: 'dessert' }
  ]}
]

// 方法
const loadTableData = async () => {
  tableLoading.value = true
  try {
    // 模拟数据
    tableData.value = generateMockTableData()
    pagination.total = 50
  } catch (error) {
    console.error('加载表格数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    tableLoading.value = false
  }
}

const loadHotDishes = async () => {
  try {
    // 模拟数据
    hotDishes.value = [
      {
        id: 1,
        name: '红烧肉',
        category: 'hot',
        image: 'https://picsum.photos/60/60?random=1',
        sales: 156,
        revenue: 4368
      },
      {
        id: 2,
        name: '宫保鸡丁',
        category: 'hot',
        image: 'https://picsum.photos/60/60?random=2',
        sales: 142,
        revenue: 3692
      },
      {
        id: 3,
        name: '清炒时蔬',
        category: 'cold',
        image: 'https://picsum.photos/60/60?random=3',
        sales: 128,
        revenue: 2048
      },
      {
        id: 4,
        name: '紫菜蛋花汤',
        category: 'soup',
        image: 'https://picsum.photos/60/60?random=4',
        sales: 98,
        revenue: 1470
      },
      {
        id: 5,
        name: '米饭',
        category: 'staple',
        image: 'https://picsum.photos/60/60?random=5',
        sales: 89,
        revenue: 267
      }
    ]
  } catch (error) {
    console.error('加载热门菜品失败:', error)
  }
}

const generateMockTableData = () => {
  const data = []
  const dishes = [
    { name: '红烧肉', category: 'hot', price: 28 },
    { name: '宫保鸡丁', category: 'hot', price: 26 },
    { name: '清炒时蔬', category: 'cold', price: 16 },
    { name: '紫菜蛋花汤', category: 'soup', price: 15 },
    { name: '米饭', category: 'staple', price: 3 }
  ]

  for (let i = 0; i < 10; i++) {
    const dish = dishes[i % dishes.length]
    const sales = Math.floor(Math.random() * 100) + 50
    data.push({
      id: i + 1,
      name: dish.name,
      category: dish.category,
      price: dish.price,
      image: `https://picsum.photos/60/60?random=${i + 1}`,
      sales,
      revenue: (sales * dish.price).toFixed(2),
      rating: (Math.random() * 2 + 3).toFixed(1)
    })
  }
  return data
}

const initSalesChart = () => {
  if (!salesChartRef.value) return

  salesChart = echarts.init(salesChartRef.value)
  updateSalesChart()
}

const updateSalesChart = () => {
  if (!salesChart) return

  const dishes = ['红烧肉', '宫保鸡丁', '清炒时蔬', '紫菜蛋花汤', '米饭']
  const sales = [156, 142, 128, 98, 89]

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: dishes
    },
    series: [
      {
        name: '销量',
        type: 'bar',
        data: sales,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#409EFF' },
              { offset: 1, color: '#67C23A' }
            ]
          }
        }
      }
    ]
  }

  salesChart.setOption(option)
}

const initRevenueChart = () => {
  if (!revenueChartRef.value) return

  revenueChart = echarts.init(revenueChartRef.value)
  refreshRevenueChart()
}

const refreshRevenueChart = () => {
  if (!revenueChart) return

  const dishes = ['红烧肉', '宫保鸡丁', '清炒时蔬', '紫菜蛋花汤', '米饭']
  const revenue = [4368, 3692, 2048, 1470, 267]

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    series: [
      {
        name: '收入',
        type: 'pie',
        radius: ['40%', '70%'],
        data: dishes.map((name, index) => ({
          value: revenue[index],
          name
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  revenueChart.setOption(option)
}

const initCategoryChart = () => {
  if (!categoryChartRef.value) return

  categoryChart = echarts.init(categoryChartRef.value)

  const data = [
    { value: 35, name: '热菜' },
    { value: 25, name: '凉菜' },
    { value: 20, name: '汤品' },
    { value: 15, name: '主食' },
    { value: 5, name: '甜品' }
  ]

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '分类销量',
        type: 'pie',
        radius: '60%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  categoryChart.setOption(option)
}

const initRatingChart = () => {
  if (!ratingChartRef.value) return

  ratingChart = echarts.init(ratingChartRef.value)

  const ratings = ['5星', '4星', '3星', '2星', '1星']
  const counts = [45, 35, 15, 3, 2]

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ratings
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '评分分布',
        type: 'bar',
        data: counts,
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  }

  ratingChart.setOption(option)
}

const refreshHotDishes = () => {
  loadHotDishes()
  ElMessage.success('热门菜品数据已刷新')
}

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadTableData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTableData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadTableData()
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 窗口大小变化时重新渲染图表
const handleResize = () => {
  if (salesChart) salesChart.resize()
  if (revenueChart) revenueChart.resize()
  if (categoryChart) categoryChart.resize()
  if (ratingChart) ratingChart.resize()
}

onMounted(async () => {
  await loadTableData()
  await loadHotDishes()

  nextTick(() => {
    initSalesChart()
    initRevenueChart()
    initCategoryChart()
    initRatingChart()
  })

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  if (salesChart) {
    salesChart.dispose()
    salesChart = null
  }
  if (revenueChart) {
    revenueChart.dispose()
    revenueChart = null
  }
  if (categoryChart) {
    categoryChart.dispose()
    categoryChart = null
  }
  if (ratingChart) {
    ratingChart.dispose()
    ratingChart = null
  }
})
</script>

<style scoped lang="scss">
.dish-analytics {
  @apply p-6 bg-gray-50 min-h-screen;
}

.stats-row {
  @apply mb-6;
}

.charts-section {
  @apply mb-6;
}

.analysis-section {
  @apply mb-6;
}

.chart-card,
.data-card {
  @apply bg-white rounded-lg shadow-sm p-6 h-full;
}

.chart-header,
.card-header {
  @apply flex justify-between items-center mb-4;

  h3 {
    @apply text-lg font-semibold text-gray-900;
  }

  .chart-controls {
    @apply flex items-center space-x-3;
  }
}

.chart-container {
  @apply h-80;
}

.hot-dishes-list {
  @apply space-y-4;
}

.dish-item {
  @apply flex items-center space-x-3 p-3 bg-gray-50 rounded-lg;

  .dish-rank {
    @apply w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white;

    &.rank-1 {
      @apply bg-yellow-500;
    }

    &.rank-2 {
      @apply bg-gray-400;
    }

    &.rank-3 {
      @apply bg-yellow-600;
    }

    &:not(.rank-1):not(.rank-2):not(.rank-3) {
      @apply bg-blue-500;
    }
  }

  .dish-image {
    @apply w-12 h-12 rounded-lg overflow-hidden;
  }

  .dish-info {
    @apply flex-1;

    .dish-name {
      @apply text-sm font-medium text-gray-900 mb-1;
    }

    .dish-category {
      @apply text-xs text-gray-500 mb-2;
    }

    .dish-stats {
      @apply flex space-x-4 text-xs;

      .sales {
        @apply text-blue-600;
      }

      .revenue {
        @apply text-green-600;
      }
    }
  }
}

.table-section {
  @apply bg-white rounded-lg shadow-sm;
}

.dish-image {
  @apply w-12 h-12 rounded-lg object-cover;
}

.revenue-text {
  @apply text-green-600 font-semibold;
}
</style>
